烧结炉集中控制与数据管理系统 - 软件需求与架构设计方案
1. 项目概述
本项目旨在开发一套基于Python的桌面应用程序，用于集中监控、控制和管理8台采用宇电AIBUS通讯协议的烧结炉。软件系统需提供直观的用户界面、强大的程序化控制能力、完善的数据记录与追溯功能以及可靠的异常报警机制，以提升生产效率、工艺稳定性和质量管理水平。
2. 功能需求规格 (FRS)
FR1.1 主监控界面:
软件启动后，默认进入主监控界面，以网格布局（如2行4列）展示8个独立的“炉子信息块”。
每个信息块对应一台烧结炉，必须清晰展示以下核心信息：
设备编号: 如“1号炉”。
当前状态: 使用醒目的文字和颜色标识，状态包括：工作中 (绿色)、空闲 (蓝色)、故障 (红色)、通讯中断 (灰色)。
当前温度 (PV): 实时刷新从仪表读取的测量值。
设定温度 (SV): 实时刷新从仪表读取的设定值。
任务时间信息:
若状态为工作中，则显示“任务开始时间”和“预计结束时间”。
若状态为空闲，则显示“下一任务预定时间”（如果已在任务调度中安排）。
FR1.2 详细监控视图:
主监控界面的每个“炉子信息块”均可点击。
点击后，弹出一个专用的“任务详情”窗口，该窗口用于显示此炉子当前正在执行任务的实时温度曲线图（设定温度曲线 vs 实际温度曲线）。
FR2.1 工艺库管理:
提供一个独立的“工艺库”管理界面。
用户可在此界面创建、编辑、删除和保存烧结工艺程序。
工艺程序的定义方式为多段式（step-by-step），每段包含“目标温度 (°C)”和“升温/保温速率 (°C/分钟)”。软件需在后台自动将速率换算为AIBUS协议所需的时间参数。
每个工艺程序可自定义名称（如“A产品-淬火工艺”）以便于识别和调用。
FR2.2 任务调度与分配:
提供一个“任务调度”界面，用于创建和管理生产任务。
创建一个新任务时，操作员必须指定以下信息：
选择工艺: 从工艺库中选择一个已定义好的烧结工艺。
指定炉子: 从8台炉子中选择一台。选择界面需实时显示各炉子的当前状态（空闲、工作中等），并优先推荐“空闲”状态的炉子。
设定开始时间: 可选择“立即开始”或指定一个未来的预定时间。
已创建的任务将进入一个任务列表，等待系统自动执行。
FR2.3 运行控制:
对于已启动并正在运行的任务，提供“强制停止”功能。
为保证工艺的严肃性，不允许在任务运行过程中修改其后续的工艺参数。
FR3.1 数据自动记录:
为每一次烧结任务自动生成一个唯一的任务ID。
系统需记录与该任务ID关联的以下信息：
执行的工艺程序ID。
任务的计划/实际开始时间、实际结束时间。
任务的最终完成状态（如“已完成”、“中途停止”）。
以固定的时间间隔（如10秒）采集并记录的时间序列数据（时间点、设定温度SV、实际温度PV）。
FR3.2 数据存储:
所有任务信息和时间序列数据均存储在后端的SQLite数据库中，确保数据的完整性和可查询性。
在每个任务成功结束后，自动在指定目录下生成一份独立的日志文件（CSV格式），作为数据备份和离线分析之用。
FR3.3 历史记录查询:
提供一个“历史任务查询”界面。
支持通过日期范围、炉子编号等条件筛选查询历史任务。
查询结果以列表形式展示任务的关键信息。
列表中的任一任务均可点击，以打开“任务详情”页面，动态绘制并展示该任务完整的历史温度曲线图。
FR3.4 报告生成与导出:
在“任务详情”页面，提供“一键导出烧结报告”功能，生成包含任务信息和温度曲线图的PDF报告文件。
在历史查询列表页面，支持勾选多个任务进行批量导出（导出为独立的CSV日志文件）。
FR4.1 报警触发条件:
通讯中断: 软件与仪表在预设时间内（如连续3次轮询）无法建立有效通讯。
温度超差: 实际温度与设定温度的偏差超出预设阈值（如±5°C）并持续一段时间（如60秒）。
仪表硬件报警: 软件能解析仪表返回的状态字节，并对仪表自身的故障（如传感器断线）进行报警。
FR4.2 报警表现形式:
视觉提示: 发生报警时，主监控界面上对应炉子的信息块背景变为醒目颜色（如红色），并显示具体的报警内容。
无声音提示: 系统不发出声音报警。
FR4.3 报警历史记录:
提供一个独立的“报警记录”窗口。
所有报警事件（包括发生时间、炉号、报警内容、解除时间）都将被自动记录在此，并可供查询。
3. 系统架构设计 (SAD)
编程语言: Python 3.x
图形界面(GUI)库: PyQt6 / PySide6
数据库: SQLite 3
核心库: pyserial (串口通讯), pandas (数据处理), pyqtgraph (高性能图表绘制)
本系统采用经典的三层架构模式，确保模块之间的高内聚、低耦合，便于开发和维护。
表现层 (Presentation Layer)
模块: UIManager
职责: 负责所有用户界面的展示和用户输入的捕获。使用PyQt构建所有窗口、控件和图表。它不包含任何业务逻辑，只负责将用户操作传递给业务逻辑层，并展示业务逻辑层返回的数据。
子模块: MainWindow, ProcessEditorWindow, TaskSchedulerWindow, HistoryViewerWindow 等。
业务逻辑层 (Business Logic Layer)
模块: TaskManager
职责: 系统的核心，驱动所有业务流程。它接收来自表现层的用户请求，并调用数据访问层和通讯服务来完成这些请求。
功能: 状态机管理（维护炉子状态）、任务调度与执行、报警逻辑判断、数据加工处理等。
数据与服务层 (Data & Service Layer)
模块: DatabaseManager, CommunicationManager
职责: 提供基础的数据和通讯服务，被业务逻辑层调用。
DatabaseManager: 封装所有对SQLite数据库的CRUD（增删改查）操作。通过定义好的函数接口（如get_task_by_id）为上层提供服务，屏蔽了底层的SQL实现细节。
CommunicationManager: 封装基于pyserial的AIBUS协议通讯。负责指令的打包、发送、接收、校验和解析。它在一个独立的后台线程中运行，以固定的频率轮询所有设备，并通过回调函数或信号/槽机制将获取到的新数据和设备状态通知给业务逻辑层。