#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务调度窗口
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QDateTimeEdit, QGroupBox, QGridLayout,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QSplitter, QWidget, QTextEdit)
from PyQt6.QtCore import Qt, QDateTime, pyqtSlot, QTimer
from PyQt6.QtGui import QFont
from datetime import datetime, timedelta

from ..core.task_manager import TaskManager
from ..models.task import TaskStatus
from ..models.furnace import FurnaceStatus
from .ui_config import UIConfig, apply_window_geometry


class TaskSchedulerWindow(QDialog):
    """任务调度窗口"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        self.task_manager = task_manager
        
        self.init_ui()
        self.load_data()
        
        # 定时刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(5000)  # 每5秒刷新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("任务调度")

        # 使用配置文件设置窗口几何
        apply_window_geometry(self, UIConfig.get_task_scheduler_geometry)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("任务调度管理")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧任务创建
        create_widget = self.create_task_creator()
        splitter.addWidget(create_widget)
        
        # 右侧任务列表
        list_widget = self.create_task_list()
        splitter.addWidget(list_widget)
        
        # 设置分割比例
        splitter.setSizes([400, 800])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_btn)
        
        self.start_task_btn = QPushButton("立即启动")
        self.start_task_btn.clicked.connect(self.start_selected_task)
        button_layout.addWidget(self.start_task_btn)
        
        self.cancel_task_btn = QPushButton("取消任务")
        self.cancel_task_btn.clicked.connect(self.cancel_selected_task)
        button_layout.addWidget(self.cancel_task_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_task_creator(self):
        """创建任务创建器"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 新建任务组
        create_group = QGroupBox("创建新任务")
        create_layout = QGridLayout(create_group)
        
        # 选择工艺
        create_layout.addWidget(QLabel("选择工艺:"), 0, 0)
        self.process_combo = QComboBox()
        create_layout.addWidget(self.process_combo, 0, 1)
        
        # 选择炉子
        create_layout.addWidget(QLabel("选择炉子:"), 1, 0)
        self.furnace_combo = QComboBox()
        create_layout.addWidget(self.furnace_combo, 1, 1)
        
        # 开始时间
        create_layout.addWidget(QLabel("开始时间:"), 2, 0)
        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDateTime(QDateTime.currentDateTime())
        self.start_time_edit.setCalendarPopup(True)
        create_layout.addWidget(self.start_time_edit, 2, 1)
        
        # 立即开始按钮
        self.immediate_btn = QPushButton("立即开始")
        self.immediate_btn.clicked.connect(self.set_immediate_start)
        create_layout.addWidget(self.immediate_btn, 3, 0, 1, 2)
        
        # 创建任务按钮
        self.create_task_btn = QPushButton("创建任务")
        self.create_task_btn.clicked.connect(self.create_task)
        create_layout.addWidget(self.create_task_btn, 4, 0, 1, 2)
        
        layout.addWidget(create_group)
        
        # 炉子状态组
        status_group = QGroupBox("炉子状态")
        status_layout = QVBoxLayout(status_group)
        
        self.furnace_status_text = QTextEdit()
        self.furnace_status_text.setMaximumHeight(200)
        self.furnace_status_text.setReadOnly(True)
        status_layout.addWidget(self.furnace_status_text)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        
        return widget
    
    def create_task_list(self):
        """创建任务列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 任务列表标题
        list_label = QLabel("任务列表")
        list_label.setFont(QFont("", 12, QFont.Weight.Bold))
        layout.addWidget(list_label)
        
        # 任务列表表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels([
            "任务ID", "工艺名称", "炉子", "状态", "计划时间", "实际时间"
        ])
        
        # 设置表格属性
        self.task_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.task_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.task_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.task_table)
        
        return widget
    
    def load_data(self):
        """加载数据"""
        self.load_processes()
        self.load_furnaces()
        self.load_tasks()
        self.update_furnace_status()
    
    def load_processes(self):
        """加载工艺列表"""
        try:
            self.process_combo.clear()
            processes = self.task_manager.db_manager.get_all_processes()
            
            for process in processes:
                self.process_combo.addItem(process.name, process.process_id)
            
        except Exception as e:
            print(f"加载工艺列表失败: {e}")
    
    def load_furnaces(self):
        """加载炉子列表"""
        try:
            self.furnace_combo.clear()
            
            for furnace_id in range(1, 9):
                furnace_data = self.task_manager.comm_manager.get_furnace_data(furnace_id)
                if furnace_data:
                    status_text = furnace_data.status.value
                    self.furnace_combo.addItem(f"{furnace_id}号炉 ({status_text})", furnace_id)
                else:
                    self.furnace_combo.addItem(f"{furnace_id}号炉 (未知)", furnace_id)
            
        except Exception as e:
            print(f"加载炉子列表失败: {e}")
    
    def load_tasks(self):
        """加载任务列表"""
        try:
            # 获取所有待执行和运行中的任务
            all_tasks = []
            
            # 待执行任务
            pending_tasks = self.task_manager.db_manager.get_pending_tasks()
            all_tasks.extend(pending_tasks)
            
            # 运行中任务
            running_tasks = self.task_manager.get_running_tasks()
            all_tasks.extend(running_tasks.values())
            
            # 最近完成的任务（最近24小时）
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            recent_tasks = self.task_manager.db_manager.get_task_history(
                start_date=start_time, end_date=end_time
            )
            all_tasks.extend(recent_tasks)
            
            # 按计划时间排序
            all_tasks.sort(key=lambda t: t.scheduled_start_time)
            
            # 填充表格
            self.task_table.setRowCount(len(all_tasks))
            
            for row, task in enumerate(all_tasks):
                # 任务ID
                task_id_item = QTableWidgetItem(task.task_id[:8] + "..." if len(task.task_id) > 8 else task.task_id)
                task_id_item.setData(Qt.ItemDataRole.UserRole, task.task_id)
                self.task_table.setItem(row, 0, task_id_item)
                
                # 工艺名称
                process = self.task_manager.db_manager.get_process(task.process_id)
                process_name = process.name if process else "未知工艺"
                self.task_table.setItem(row, 1, QTableWidgetItem(process_name))
                
                # 炉子
                self.task_table.setItem(row, 2, QTableWidgetItem(f"{task.furnace_id}号炉"))
                
                # 状态
                status_item = QTableWidgetItem(task.status.value)
                # 根据状态设置颜色
                if task.status == TaskStatus.RUNNING:
                    status_item.setBackground(Qt.GlobalColor.green)
                elif task.status == TaskStatus.PENDING:
                    status_item.setBackground(Qt.GlobalColor.yellow)
                elif task.status == TaskStatus.COMPLETED:
                    status_item.setBackground(Qt.GlobalColor.lightGray)
                elif task.status in [TaskStatus.STOPPED, TaskStatus.FAILED]:
                    status_item.setBackground(Qt.GlobalColor.red)
                
                self.task_table.setItem(row, 3, status_item)
                
                # 计划时间
                scheduled_time = task.scheduled_start_time.strftime("%m-%d %H:%M")
                self.task_table.setItem(row, 4, QTableWidgetItem(scheduled_time))
                
                # 实际时间
                if task.actual_start_time:
                    actual_time = task.actual_start_time.strftime("%m-%d %H:%M")
                    if task.actual_end_time:
                        actual_time += f" - {task.actual_end_time.strftime('%H:%M')}"
                else:
                    actual_time = "未开始"
                
                self.task_table.setItem(row, 5, QTableWidgetItem(actual_time))
            
        except Exception as e:
            print(f"加载任务列表失败: {e}")
    
    def update_furnace_status(self):
        """更新炉子状态"""
        try:
            status_text = "炉子状态:\n"
            
            for furnace_id in range(1, 9):
                furnace_data = self.task_manager.comm_manager.get_furnace_data(furnace_id)
                if furnace_data:
                    status_text += f"{furnace_id}号炉: {furnace_data.status.value} "
                    status_text += f"(PV:{furnace_data.current_temp:.1f}°C, SV:{furnace_data.set_temp:.1f}°C)\n"
                else:
                    status_text += f"{furnace_id}号炉: 未知状态\n"
            
            self.furnace_status_text.setText(status_text)
            
        except Exception as e:
            print(f"更新炉子状态失败: {e}")
    
    @pyqtSlot()
    def set_immediate_start(self):
        """设置立即开始"""
        self.start_time_edit.setDateTime(QDateTime.currentDateTime())
    
    @pyqtSlot()
    def create_task(self):
        """创建任务"""
        try:
            # 获取选择的工艺
            process_id = self.process_combo.currentData()
            if not process_id:
                QMessageBox.warning(self, "警告", "请选择工艺")
                return
            
            # 获取选择的炉子
            furnace_id = self.furnace_combo.currentData()
            if not furnace_id:
                QMessageBox.warning(self, "警告", "请选择炉子")
                return
            
            # 获取开始时间
            start_time = self.start_time_edit.dateTime().toPython()
            
            # 检查炉子状态
            furnace_data = self.task_manager.comm_manager.get_furnace_data(furnace_id)
            if not furnace_data or furnace_data.status == FurnaceStatus.COMM_ERROR:
                QMessageBox.warning(self, "警告", f"{furnace_id}号炉通讯异常，无法创建任务")
                return
            
            if furnace_id in self.task_manager.get_running_tasks():
                QMessageBox.warning(self, "警告", f"{furnace_id}号炉正在执行任务")
                return
            
            # 创建任务
            task_id = self.task_manager.create_task(process_id, furnace_id, start_time)
            
            if task_id:
                QMessageBox.information(self, "成功", f"任务创建成功: {task_id}")
                self.load_tasks()
            else:
                QMessageBox.warning(self, "失败", "任务创建失败")
            
        except Exception as e:
            print(f"创建任务失败: {e}")
            QMessageBox.warning(self, "错误", f"创建任务失败: {e}")
    
    @pyqtSlot()
    def start_selected_task(self):
        """启动选中的任务"""
        try:
            current_row = self.task_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请选择一个任务")
                return
            
            task_id_item = self.task_table.item(current_row, 0)
            if not task_id_item:
                return
            
            task_id = task_id_item.data(Qt.ItemDataRole.UserRole)
            
            success = self.task_manager.start_task(task_id)
            if success:
                QMessageBox.information(self, "成功", "任务已启动")
                self.load_tasks()
            else:
                QMessageBox.warning(self, "失败", "启动任务失败")
            
        except Exception as e:
            print(f"启动任务失败: {e}")
            QMessageBox.warning(self, "错误", f"启动任务失败: {e}")
    
    @pyqtSlot()
    def cancel_selected_task(self):
        """取消选中的任务"""
        try:
            current_row = self.task_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请选择一个任务")
                return
            
            task_id_item = self.task_table.item(current_row, 0)
            if not task_id_item:
                return
            
            task_id = task_id_item.data(Qt.ItemDataRole.UserRole)
            
            # 这里可以实现取消任务的逻辑
            QMessageBox.information(self, "提示", "取消任务功能待实现")
            
        except Exception as e:
            print(f"取消任务失败: {e}")
    
    @pyqtSlot()
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
    
    def closeEvent(self, event):
        """关闭事件"""
        self.refresh_timer.stop()
        event.accept()
