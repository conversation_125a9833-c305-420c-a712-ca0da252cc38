#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通讯管理器
"""

import serial
import threading
import time
from typing import Dict, Optional, Callable, List
from datetime import datetime, timedelta
from PyQt6.QtCore import QObject, pyqtSignal

from .aibus_protocol import AIBUSProtocol, AIBUSCommand
from ..models.furnace import FurnaceData, FurnaceStatus


class CommunicationManager(QObject):
    """通讯管理器"""
    
    # 信号定义
    furnace_data_updated = pyqtSignal(int, FurnaceData)  # 炉子数据更新
    communication_error = pyqtSignal(int, str)          # 通讯错误
    device_status_changed = pyqtSignal(int, bool)       # 设备状态变化
    
    def __init__(self, port: str = "COM1", baudrate: int = 9600, 
                 timeout: float = 1.0, poll_interval: float = 2.0):
        super().__init__()
        
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.poll_interval = poll_interval
        
        self.protocol = AIBUSProtocol()
        self.serial_conn: Optional[serial.Serial] = None
        self.is_running = False
        self.poll_thread: Optional[threading.Thread] = None
        
        # 炉子配置 (炉子ID -> 设备地址)
        self.furnace_addresses = {i: i for i in range(1, 9)}  # 1-8号炉对应地址1-8
        
        # 炉子数据缓存
        self.furnace_data: Dict[int, FurnaceData] = {}
        
        # 通讯状态跟踪
        self.last_comm_time: Dict[int, datetime] = {}
        self.comm_error_count: Dict[int, int] = {}
        self.max_error_count = 3
        
        # 初始化炉子数据
        self._initialize_furnace_data()
    
    def _initialize_furnace_data(self):
        """初始化炉子数据"""
        for furnace_id in self.furnace_addresses.keys():
            self.furnace_data[furnace_id] = FurnaceData(
                furnace_id=furnace_id,
                status=FurnaceStatus.COMM_ERROR,
                current_temp=0.0,
                set_temp=0.0,
                timestamp=datetime.now()
            )
            self.comm_error_count[furnace_id] = 0
    
    def start_communication(self) -> bool:
        """启动通讯"""
        try:
            # 检查串口是否存在
            import serial.tools.list_ports
            available_ports = [port.device for port in serial.tools.list_ports.comports()]

            if self.port not in available_ports:
                print(f"警告: 串口 {self.port} 不存在")
                print(f"可用串口: {available_ports}")
                print("系统将在模拟模式下运行")

                # 启动模拟模式
                self.is_running = True
                self.poll_thread = threading.Thread(target=self._simulate_devices, daemon=True)
                self.poll_thread.start()
                return True

            # 尝试打开串口
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                bytesize=serial.EIGHTBITS
            )

            self.is_running = True
            self.poll_thread = threading.Thread(target=self._poll_devices, daemon=True)
            self.poll_thread.start()

            print(f"通讯已启动: {self.port}, {self.baudrate}")
            return True

        except serial.SerialException as e:
            print(f"串口错误: {e}")
            print("系统将在模拟模式下运行")

            # 启动模拟模式
            self.is_running = True
            self.poll_thread = threading.Thread(target=self._simulate_devices, daemon=True)
            self.poll_thread.start()
            return True

        except Exception as e:
            print(f"启动通讯失败: {e}")
            print("系统将在模拟模式下运行")

            # 启动模拟模式
            self.is_running = True
            self.poll_thread = threading.Thread(target=self._simulate_devices, daemon=True)
            self.poll_thread.start()
            return True
    
    def stop_communication(self):
        """停止通讯"""
        self.is_running = False
        
        if self.poll_thread and self.poll_thread.is_alive():
            self.poll_thread.join(timeout=5.0)
        
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
        
        print("通讯已停止")
    
    def _poll_devices(self):
        """轮询设备"""
        while self.is_running:
            for furnace_id in self.furnace_addresses.keys():
                if not self.is_running:
                    break
                
                try:
                    self._poll_single_device(furnace_id)
                    time.sleep(0.1)  # 设备间间隔
                    
                except Exception as e:
                    print(f"轮询炉子{furnace_id}失败: {e}")
                    self._handle_communication_error(furnace_id, str(e))
            
            time.sleep(self.poll_interval)
    
    def _poll_single_device(self, furnace_id: int):
        """轮询单个设备"""
        device_addr = self.furnace_addresses[furnace_id]
        
        # 读取PV值
        pv_value = self._read_temperature(device_addr, AIBUSCommand.READ_PV)
        if pv_value is None:
            raise Exception("读取PV失败")
        
        # 读取SV值
        sv_value = self._read_temperature(device_addr, AIBUSCommand.READ_SV)
        if sv_value is None:
            raise Exception("读取SV失败")
        
        # 读取状态
        status_info = self._read_device_status(device_addr)
        
        # 更新炉子数据
        furnace_data = self.furnace_data[furnace_id]
        furnace_data.current_temp = pv_value
        furnace_data.set_temp = sv_value
        furnace_data.timestamp = datetime.now()
        
        # 根据状态信息确定炉子状态
        if status_info and status_info.get('sensor_error', False):
            furnace_data.status = FurnaceStatus.FAULT
        elif abs(pv_value - sv_value) > 5.0:  # 温差超过5度认为在工作
            furnace_data.status = FurnaceStatus.WORKING
        else:
            furnace_data.status = FurnaceStatus.IDLE
        
        # 重置错误计数
        self.comm_error_count[furnace_id] = 0
        self.last_comm_time[furnace_id] = datetime.now()
        
        # 发送信号
        self.furnace_data_updated.emit(furnace_id, furnace_data)
        self.device_status_changed.emit(furnace_id, True)

    def _read_temperature(self, device_addr: int, command: AIBUSCommand) -> Optional[float]:
        """读取温度值"""
        if not self.serial_conn or not self.serial_conn.is_open:
            return None

        try:
            # 构建命令
            if command == AIBUSCommand.READ_PV:
                cmd_frame = self.protocol.build_read_pv_command(device_addr)
            elif command == AIBUSCommand.READ_SV:
                cmd_frame = self.protocol.build_read_sv_command(device_addr)
            else:
                return None

            # 发送命令
            self.serial_conn.write(cmd_frame)

            # 读取响应
            response = self.serial_conn.read(10)  # 最多读取10字节
            if not response:
                return None

            # 解析响应
            success, parsed_data = self.protocol.parse_response(response)
            if not success or not parsed_data:
                return None

            # 解析温度值
            return self.protocol.parse_temperature_response(parsed_data['data'])

        except Exception as e:
            print(f"读取温度失败 (设备{device_addr}): {e}")
            return None

    def _read_device_status(self, device_addr: int) -> Optional[dict]:
        """读取设备状态"""
        if not self.serial_conn or not self.serial_conn.is_open:
            return None

        try:
            # 构建状态查询命令
            cmd_frame = self.protocol.build_read_status_command(device_addr)

            # 发送命令
            self.serial_conn.write(cmd_frame)

            # 读取响应
            response = self.serial_conn.read(10)
            if not response:
                return None

            # 解析响应
            success, parsed_data = self.protocol.parse_response(response)
            if not success or not parsed_data:
                return None

            # 解析状态
            return self.protocol.parse_status_response(parsed_data['data'])

        except Exception as e:
            print(f"读取状态失败 (设备{device_addr}): {e}")
            return None

    def _handle_communication_error(self, furnace_id: int, error_msg: str):
        """处理通讯错误"""
        self.comm_error_count[furnace_id] += 1

        if self.comm_error_count[furnace_id] >= self.max_error_count:
            # 标记为通讯中断
            furnace_data = self.furnace_data[furnace_id]
            furnace_data.status = FurnaceStatus.COMM_ERROR
            furnace_data.timestamp = datetime.now()

            # 发送信号
            self.furnace_data_updated.emit(furnace_id, furnace_data)
            self.communication_error.emit(furnace_id, error_msg)
            self.device_status_changed.emit(furnace_id, False)

    def write_set_temperature(self, furnace_id: int, temperature: float) -> bool:
        """写入设定温度"""
        if furnace_id not in self.furnace_addresses:
            return False

        device_addr = self.furnace_addresses[furnace_id]

        if not self.serial_conn or not self.serial_conn.is_open:
            return False

        try:
            # 构建写入SV命令
            cmd_frame = self.protocol.build_write_sv_command(device_addr, temperature)

            # 发送命令
            self.serial_conn.write(cmd_frame)

            # 读取响应确认
            response = self.serial_conn.read(10)
            if not response:
                return False

            # 解析响应
            success, parsed_data = self.protocol.parse_response(response)

            if success:
                print(f"设定温度成功: 炉子{furnace_id}, 温度{temperature}°C")
                return True
            else:
                print(f"设定温度失败: 炉子{furnace_id}, 响应解析错误")
                return False

        except Exception as e:
            print(f"设定温度失败: 炉子{furnace_id}, {e}")
            return False

    def get_furnace_data(self, furnace_id: int) -> Optional[FurnaceData]:
        """获取炉子数据"""
        return self.furnace_data.get(furnace_id)

    def get_all_furnace_data(self) -> Dict[int, FurnaceData]:
        """获取所有炉子数据"""
        return self.furnace_data.copy()

    def is_device_online(self, furnace_id: int) -> bool:
        """检查设备是否在线"""
        if furnace_id not in self.last_comm_time:
            return False

        last_time = self.last_comm_time[furnace_id]
        timeout_threshold = timedelta(seconds=self.poll_interval * 3)

        return datetime.now() - last_time < timeout_threshold

    def set_poll_interval(self, interval: float):
        """设置轮询间隔"""
        self.poll_interval = max(0.5, interval)  # 最小0.5秒

    def configure_furnace_address(self, furnace_id: int, device_addr: int):
        """配置炉子设备地址"""
        if 1 <= device_addr <= 247:
            self.furnace_addresses[furnace_id] = device_addr

    def _simulate_devices(self):
        """模拟设备数据（用于无串口环境）"""
        import random
        import math

        print("启动设备模拟模式")

        # 初始化模拟数据
        sim_data = {}
        for furnace_id in self.furnace_addresses.keys():
            sim_data[furnace_id] = {
                'target_temp': 20.0,
                'current_temp': 20.0,
                'heating': False,
                'last_update': time.time()
            }

        while self.is_running:
            try:
                for furnace_id in self.furnace_addresses.keys():
                    if not self.is_running:
                        break

                    # 模拟温度变化
                    data = sim_data[furnace_id]
                    current_time = time.time()
                    time_diff = current_time - data['last_update']

                    # 模拟温度控制
                    temp_diff = data['target_temp'] - data['current_temp']

                    if abs(temp_diff) > 1.0:
                        # 需要加热或冷却
                        if temp_diff > 0:
                            # 加热
                            data['current_temp'] += min(2.0 * time_diff, abs(temp_diff))
                            data['heating'] = True
                        else:
                            # 冷却
                            data['current_temp'] -= min(1.0 * time_diff, abs(temp_diff))
                            data['heating'] = False
                    else:
                        # 温度稳定
                        data['heating'] = False
                        # 添加小幅波动
                        data['current_temp'] += random.uniform(-0.5, 0.5)

                    data['last_update'] = current_time

                    # 更新炉子数据
                    furnace_data = self.furnace_data[furnace_id]
                    furnace_data.current_temp = round(data['current_temp'], 1)
                    furnace_data.set_temp = round(data['target_temp'], 1)
                    furnace_data.timestamp = datetime.now()

                    # 确定状态
                    if abs(furnace_data.current_temp - furnace_data.set_temp) > 5.0:
                        furnace_data.status = FurnaceStatus.WORKING
                    else:
                        furnace_data.status = FurnaceStatus.IDLE

                    # 重置错误计数
                    self.comm_error_count[furnace_id] = 0
                    self.last_comm_time[furnace_id] = datetime.now()

                    # 发送信号
                    self.furnace_data_updated.emit(furnace_id, furnace_data)
                    self.device_status_changed.emit(furnace_id, True)

                    time.sleep(0.1)  # 设备间间隔

                time.sleep(self.poll_interval)

            except Exception as e:
                print(f"模拟设备错误: {e}")
                time.sleep(1.0)

    def write_set_temperature(self, furnace_id: int, temperature: float) -> bool:
        """写入设定温度"""
        if furnace_id not in self.furnace_addresses:
            return False

        # 如果是模拟模式
        if not self.serial_conn:
            try:
                # 更新模拟目标温度
                if hasattr(self, '_simulate_devices'):
                    # 这里可以更新模拟数据的目标温度
                    pass

                # 直接更新设定温度
                furnace_data = self.furnace_data[furnace_id]
                furnace_data.set_temp = temperature

                print(f"模拟设定温度: 炉子{furnace_id}, 温度{temperature}°C")
                return True
            except Exception as e:
                print(f"模拟设定温度失败: {e}")
                return False

        # 真实串口通讯
        device_addr = self.furnace_addresses[furnace_id]

        if not self.serial_conn or not self.serial_conn.is_open:
            return False

        try:
            # 构建写入SV命令
            cmd_frame = self.protocol.build_write_sv_command(device_addr, temperature)

            # 发送命令
            self.serial_conn.write(cmd_frame)

            # 读取响应确认
            response = self.serial_conn.read(10)
            if not response:
                return False

            # 解析响应
            success, parsed_data = self.protocol.parse_response(response)

            if success:
                print(f"设定温度成功: 炉子{furnace_id}, 温度{temperature}°C")
                return True
            else:
                print(f"设定温度失败: 炉子{furnace_id}, 响应解析错误")
                return False

        except Exception as e:
            print(f"设定温度失败: 炉子{furnace_id}, {e}")
            return False
