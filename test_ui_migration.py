#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI重构测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication


def test_original_ui():
    """测试原版UI"""
    print("🔍 测试原版UI...")
    try:
        from src.ui.main_window import MainWindow as OriginalMainWindow
        print("  ✓ 原版UI导入成功")
        return True
    except Exception as e:
        print(f"  ❌ 原版UI导入失败: {e}")
        return False


def test_designer_ui():
    """测试Designer UI"""
    print("🔍 测试Designer UI...")
    try:
        from src.ui.main_window_new import MainWindow as DesignerMainWindow
        print("  ✓ Designer UI导入成功")
        return True
    except Exception as e:
        print(f"  ❌ Designer UI导入失败: {e}")
        return False


def test_ui_files():
    """测试UI文件"""
    print("🔍 测试UI文件...")
    
    ui_files = [
        "src/ui/ui_files/main_window.ui",
        "src/ui/ui_files/furnace_widget.ui",
        "src/ui/ui_files/ui_main_window.py",
        "src/ui/ui_files/ui_furnace_widget.py"
    ]
    
    all_exist = True
    for ui_file in ui_files:
        if Path(ui_file).exists():
            print(f"  ✓ {ui_file}")
        else:
            print(f"  ❌ {ui_file} 不存在")
            all_exist = False
    
    return all_exist


def main():
    """主测试函数"""
    print("UI重构测试")
    print("=" * 50)
    
    # 测试各个组件
    original_ok = test_original_ui()
    designer_ok = test_designer_ui()
    ui_files_ok = test_ui_files()
    
    print("\n📊 测试结果:")
    print(f"  原版UI: {'✅' if original_ok else '❌'}")
    print(f"  Designer UI: {'✅' if designer_ok else '❌'}")
    print(f"  UI文件: {'✅' if ui_files_ok else '❌'}")

    if designer_ok and ui_files_ok:
        print("\n🎉 重构成功! 可以使用Designer UI")
        print("💡 运行命令: python main_designer.py")
    elif original_ok:
        print("\n⚠️  Designer UI未就绪，但原版UI可用")
        print("💡 运行命令: python main.py")
    else:
        print("\n❌ 所有UI都不可用，请检查代码")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
