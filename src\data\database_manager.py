#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
"""

import sqlite3
import os
import uuid
from datetime import datetime
from typing import List, Optional, Tuple
from contextlib import contextmanager

from ..models.process import Process, ProcessStep
from ..models.task import Task, TaskStatus, TemperatureRecord
from ..models.furnace import AlarmData


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "furnace_control.db"):
        self.db_path = db_path
        self.ensure_db_directory()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def initialize_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建工艺程序表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processes (
                    process_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_time TIMESTAMP,
                    updated_time TIMESTAMP
                )
            ''')
            
            # 创建工艺步骤表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS process_steps (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    process_id TEXT,
                    step_id INTEGER,
                    target_temp REAL,
                    rate REAL,
                    duration INTEGER,
                    FOREIGN KEY (process_id) REFERENCES processes (process_id)
                )
            ''')
            
            # 创建任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    task_id TEXT PRIMARY KEY,
                    process_id TEXT,
                    furnace_id INTEGER,
                    status TEXT,
                    scheduled_start_time TIMESTAMP,
                    actual_start_time TIMESTAMP,
                    actual_end_time TIMESTAMP,
                    created_time TIMESTAMP,
                    current_step INTEGER DEFAULT 0,
                    FOREIGN KEY (process_id) REFERENCES processes (process_id)
                )
            ''')
            
            # 创建温度记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS temperature_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT,
                    timestamp TIMESTAMP,
                    set_temp REAL,
                    actual_temp REAL,
                    step_id INTEGER DEFAULT 0,
                    FOREIGN KEY (task_id) REFERENCES tasks (task_id)
                )
            ''')
            
            # 创建报警记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alarms (
                    alarm_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    furnace_id INTEGER,
                    alarm_type TEXT,
                    alarm_message TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            conn.commit()
    
    # 工艺程序相关方法
    def save_process(self, process: Process) -> str:
        """保存工艺程序"""
        if not process.process_id:
            process.process_id = str(uuid.uuid4())
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 保存工艺程序基本信息
            cursor.execute('''
                INSERT OR REPLACE INTO processes 
                (process_id, name, description, created_time, updated_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (process.process_id, process.name, process.description,
                  process.created_time, process.updated_time))
            
            # 删除旧的步骤
            cursor.execute('DELETE FROM process_steps WHERE process_id = ?', 
                          (process.process_id,))
            
            # 保存工艺步骤
            for step in process.steps:
                cursor.execute('''
                    INSERT INTO process_steps 
                    (process_id, step_id, target_temp, rate, duration)
                    VALUES (?, ?, ?, ?, ?)
                ''', (process.process_id, step.step_id, step.target_temp,
                      step.rate, step.duration))
            
            conn.commit()
        
        return process.process_id

    def get_process(self, process_id: str) -> Optional[Process]:
        """获取工艺程序"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取工艺程序基本信息
            cursor.execute('''
                SELECT * FROM processes WHERE process_id = ?
            ''', (process_id,))

            row = cursor.fetchone()
            if not row:
                return None

            process = Process(
                process_id=row['process_id'],
                name=row['name'],
                description=row['description'],
                created_time=datetime.fromisoformat(row['created_time']),
                updated_time=datetime.fromisoformat(row['updated_time'])
            )

            # 获取工艺步骤
            cursor.execute('''
                SELECT * FROM process_steps
                WHERE process_id = ?
                ORDER BY step_id
            ''', (process_id,))

            for step_row in cursor.fetchall():
                step = ProcessStep(
                    step_id=step_row['step_id'],
                    target_temp=step_row['target_temp'],
                    rate=step_row['rate'],
                    duration=step_row['duration']
                )
                process.steps.append(step)

            return process

    def get_all_processes(self) -> List[Process]:
        """获取所有工艺程序"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT process_id FROM processes ORDER BY name')

            processes = []
            for row in cursor.fetchall():
                process = self.get_process(row['process_id'])
                if process:
                    processes.append(process)

            return processes

    def delete_process(self, process_id: str) -> bool:
        """删除工艺程序"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否有任务在使用此工艺
            cursor.execute('''
                SELECT COUNT(*) FROM tasks
                WHERE process_id = ? AND status IN ('待执行', '执行中')
            ''', (process_id,))

            if cursor.fetchone()[0] > 0:
                return False  # 有活动任务，不能删除

            # 删除工艺步骤
            cursor.execute('DELETE FROM process_steps WHERE process_id = ?',
                          (process_id,))

            # 删除工艺程序
            cursor.execute('DELETE FROM processes WHERE process_id = ?',
                          (process_id,))

            conn.commit()
            return True

    # 任务相关方法
    def save_task(self, task: Task) -> str:
        """保存任务"""
        if not task.task_id:
            task.task_id = str(uuid.uuid4())

        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO tasks
                (task_id, process_id, furnace_id, status, scheduled_start_time,
                 actual_start_time, actual_end_time, created_time, current_step)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (task.task_id, task.process_id, task.furnace_id,
                  task.status.value, task.scheduled_start_time,
                  task.actual_start_time, task.actual_end_time,
                  task.created_time, task.current_step))

            conn.commit()

        return task.task_id

    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM tasks WHERE task_id = ?', (task_id,))
            row = cursor.fetchone()

            if not row:
                return None

            task = Task(
                task_id=row['task_id'],
                process_id=row['process_id'],
                furnace_id=row['furnace_id'],
                status=TaskStatus(row['status']),
                scheduled_start_time=datetime.fromisoformat(row['scheduled_start_time']),
                actual_start_time=datetime.fromisoformat(row['actual_start_time']) if row['actual_start_time'] else None,
                actual_end_time=datetime.fromisoformat(row['actual_end_time']) if row['actual_end_time'] else None,
                created_time=datetime.fromisoformat(row['created_time']),
                current_step=row['current_step']
            )

            # 获取温度记录
            cursor.execute('''
                SELECT * FROM temperature_records
                WHERE task_id = ?
                ORDER BY timestamp
            ''', (task_id,))

            for temp_row in cursor.fetchall():
                record = TemperatureRecord(
                    timestamp=datetime.fromisoformat(temp_row['timestamp']),
                    set_temp=temp_row['set_temp'],
                    actual_temp=temp_row['actual_temp'],
                    step_id=temp_row['step_id']
                )
                task.temperature_records.append(record)

            return task

    def get_tasks_by_furnace(self, furnace_id: int, status: Optional[TaskStatus] = None) -> List[Task]:
        """获取指定炉子的任务"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if status:
                cursor.execute('''
                    SELECT task_id FROM tasks
                    WHERE furnace_id = ? AND status = ?
                    ORDER BY scheduled_start_time
                ''', (furnace_id, status.value))
            else:
                cursor.execute('''
                    SELECT task_id FROM tasks
                    WHERE furnace_id = ?
                    ORDER BY scheduled_start_time
                ''', (furnace_id,))

            tasks = []
            for row in cursor.fetchall():
                task = self.get_task(row['task_id'])
                if task:
                    tasks.append(task)

            return tasks

    def get_pending_tasks(self) -> List[Task]:
        """获取待执行的任务"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT task_id FROM tasks
                WHERE status = ?
                ORDER BY scheduled_start_time
            ''', (TaskStatus.PENDING.value,))

            tasks = []
            for row in cursor.fetchall():
                task = self.get_task(row['task_id'])
                if task:
                    tasks.append(task)

            return tasks

    def save_temperature_record(self, task_id: str, set_temp: float,
                               actual_temp: float, step_id: int = 0):
        """保存温度记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO temperature_records
                (task_id, timestamp, set_temp, actual_temp, step_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (task_id, datetime.now(), set_temp, actual_temp, step_id))

            conn.commit()

    def update_task_status(self, task_id: str, status: TaskStatus,
                          current_step: Optional[int] = None):
        """更新任务状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            update_fields = ['status = ?']
            params = [status.value]

            if status == TaskStatus.RUNNING and current_step is None:
                update_fields.append('actual_start_time = ?')
                params.append(datetime.now())
            elif status in [TaskStatus.COMPLETED, TaskStatus.STOPPED, TaskStatus.FAILED]:
                update_fields.append('actual_end_time = ?')
                params.append(datetime.now())

            if current_step is not None:
                update_fields.append('current_step = ?')
                params.append(current_step)

            params.append(task_id)

            cursor.execute(f'''
                UPDATE tasks SET {', '.join(update_fields)}
                WHERE task_id = ?
            ''', params)

            conn.commit()

    # 报警相关方法
    def save_alarm(self, alarm: AlarmData) -> int:
        """保存报警记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO alarms
                (furnace_id, alarm_type, alarm_message, start_time, end_time, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (alarm.furnace_id, alarm.alarm_type, alarm.alarm_message,
                  alarm.start_time, alarm.end_time, alarm.is_active))

            alarm_id = cursor.lastrowid
            conn.commit()

            return alarm_id

    def update_alarm(self, alarm_id: int, end_time: datetime, is_active: bool = False):
        """更新报警状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE alarms
                SET end_time = ?, is_active = ?
                WHERE alarm_id = ?
            ''', (end_time, is_active, alarm_id))

            conn.commit()

    def get_active_alarms(self, furnace_id: Optional[int] = None) -> List[AlarmData]:
        """获取活动报警"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if furnace_id:
                cursor.execute('''
                    SELECT * FROM alarms
                    WHERE furnace_id = ? AND is_active = 1
                    ORDER BY start_time DESC
                ''', (furnace_id,))
            else:
                cursor.execute('''
                    SELECT * FROM alarms
                    WHERE is_active = 1
                    ORDER BY start_time DESC
                ''')

            alarms = []
            for row in cursor.fetchall():
                alarm = AlarmData(
                    alarm_id=row['alarm_id'],
                    furnace_id=row['furnace_id'],
                    alarm_type=row['alarm_type'],
                    alarm_message=row['alarm_message'],
                    start_time=datetime.fromisoformat(row['start_time']),
                    end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                    is_active=bool(row['is_active'])
                )
                alarms.append(alarm)

            return alarms

    def get_alarm_history(self, furnace_id: Optional[int] = None,
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None) -> List[AlarmData]:
        """获取报警历史"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            conditions = []
            params = []

            if furnace_id:
                conditions.append('furnace_id = ?')
                params.append(furnace_id)

            if start_date:
                conditions.append('start_time >= ?')
                params.append(start_date)

            if end_date:
                conditions.append('start_time <= ?')
                params.append(end_date)

            where_clause = ' AND '.join(conditions) if conditions else '1=1'

            cursor.execute(f'''
                SELECT * FROM alarms
                WHERE {where_clause}
                ORDER BY start_time DESC
            ''', params)

            alarms = []
            for row in cursor.fetchall():
                alarm = AlarmData(
                    alarm_id=row['alarm_id'],
                    furnace_id=row['furnace_id'],
                    alarm_type=row['alarm_type'],
                    alarm_message=row['alarm_message'],
                    start_time=datetime.fromisoformat(row['start_time']),
                    end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                    is_active=bool(row['is_active'])
                )
                alarms.append(alarm)

            return alarms

    # 历史查询方法
    def get_task_history(self, furnace_id: Optional[int] = None,
                        start_date: Optional[datetime] = None,
                        end_date: Optional[datetime] = None,
                        status: Optional[TaskStatus] = None) -> List[Task]:
        """获取任务历史"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            conditions = []
            params = []

            if furnace_id:
                conditions.append('furnace_id = ?')
                params.append(furnace_id)

            if start_date:
                conditions.append('actual_start_time >= ?')
                params.append(start_date)

            if end_date:
                conditions.append('actual_start_time <= ?')
                params.append(end_date)

            if status:
                conditions.append('status = ?')
                params.append(status.value)

            where_clause = ' AND '.join(conditions) if conditions else '1=1'

            cursor.execute(f'''
                SELECT task_id FROM tasks
                WHERE {where_clause}
                ORDER BY actual_start_time DESC
            ''', params)

            tasks = []
            for row in cursor.fetchall():
                task = self.get_task(row['task_id'])
                if task:
                    tasks.append(task)

            return tasks

    def export_task_data_to_csv(self, task_id: str, file_path: str) -> bool:
        """导出任务数据到CSV文件"""
        import csv

        task = self.get_task(task_id)
        if not task:
            return False

        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # 写入头部信息
                writer.writerow(['任务ID', task.task_id])
                writer.writerow(['工艺ID', task.process_id])
                writer.writerow(['炉子编号', task.furnace_id])
                writer.writerow(['任务状态', task.status.value])
                writer.writerow(['计划开始时间', task.scheduled_start_time])
                writer.writerow(['实际开始时间', task.actual_start_time])
                writer.writerow(['实际结束时间', task.actual_end_time])
                writer.writerow([])  # 空行

                # 写入温度数据头
                writer.writerow(['时间戳', '设定温度(°C)', '实际温度(°C)', '步骤ID'])

                # 写入温度数据
                for record in task.temperature_records:
                    writer.writerow([
                        record.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        record.set_temp,
                        record.actual_temp,
                        record.step_id
                    ])

            return True
        except Exception as e:
            print(f"导出CSV文件失败: {e}")
            return False
