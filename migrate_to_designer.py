#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI重构迁移脚本
从原有代码迁移到Qt Designer架构
"""

import os
import shutil
import sys
from pathlib import Path


def backup_original_files():
    """备份原有文件"""
    print("📦 备份原有文件...")
    
    backup_dir = Path("backup_original_ui")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "src/ui/main_window.py",
        "src/ui/furnace_widget.py"
    ]
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"  ✓ 备份: {file_path} -> {backup_path}")
        else:
            print(f"  ⚠️  文件不存在: {file_path}")


def create_migration_main():
    """创建迁移版本的main.py"""
    print("🔄 创建迁移版本的main.py...")
    
    main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烧结炉集中控制与数据管理系统 - Qt Designer重构版本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from src.core.task_manager import TaskManager
from src.data.database_manager import DatabaseManager
from src.communication.communication_manager import CommunicationManager

# 选择UI版本
USE_DESIGNER_UI = True  # 设置为False使用原有UI

if USE_DESIGNER_UI:
    from src.ui.main_window_new import MainWindow
else:
    from src.ui.main_window import MainWindow


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("烧结炉集中控制与数据管理系统")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("工业控制系统")
    
    try:
        print("🚀 启动烧结炉集中控制与数据管理系统...")
        print(f"📱 UI版本: {'Qt Designer重构版' if USE_DESIGNER_UI else '原版'}")
        
        # 初始化数据库
        print("📊 初始化数据库...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # 初始化通讯管理器
        print("📡 初始化通讯管理器...")
        comm_manager = CommunicationManager()
        
        # 初始化任务管理器
        print("⚙️  初始化任务管理器...")
        task_manager = TaskManager(db_manager, comm_manager)
        
        # 创建主窗口
        print("🖥️  创建主窗口...")
        main_window = MainWindow(task_manager)
        main_window.show()
        
        print("✅ 系统启动成功!")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open("main_designer.py", "w", encoding="utf-8") as f:
        f.write(main_content)
    
    print("  ✓ 创建: main_designer.py")


def update_imports():
    """更新导入语句"""
    print("📝 创建导入兼容性文件...")
    
    # 创建一个兼容性导入文件
    compat_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI兼容性导入文件
支持在新旧UI之间切换
"""

import os

# 环境变量控制UI版本
USE_DESIGNER_UI = os.getenv('USE_DESIGNER_UI', 'true').lower() == 'true'

if USE_DESIGNER_UI:
    try:
        from .main_window_new import MainWindow
        from .furnace_widget_new import FurnaceWidget
        print("✓ 使用Qt Designer重构版UI")
    except ImportError as e:
        print(f"⚠️  Designer UI导入失败，回退到原版: {e}")
        from .main_window import MainWindow
        from .furnace_widget import FurnaceWidget
else:
    from .main_window import MainWindow
    from .furnace_widget import FurnaceWidget
    print("✓ 使用原版UI")

__all__ = ['MainWindow', 'FurnaceWidget']
'''
    
    with open("src/ui/ui_compat.py", "w", encoding="utf-8") as f:
        f.write(compat_content)
    
    print("  ✓ 创建: src/ui/ui_compat.py")


def create_test_script():
    """创建测试脚本"""
    print("🧪 创建测试脚本...")
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI重构测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication


def test_original_ui():
    """测试原版UI"""
    print("🔍 测试原版UI...")
    try:
        from src.ui.main_window import MainWindow as OriginalMainWindow
        print("  ✓ 原版UI导入成功")
        return True
    except Exception as e:
        print(f"  ❌ 原版UI导入失败: {e}")
        return False


def test_designer_ui():
    """测试Designer UI"""
    print("🔍 测试Designer UI...")
    try:
        from src.ui.main_window_new import MainWindow as DesignerMainWindow
        print("  ✓ Designer UI导入成功")
        return True
    except Exception as e:
        print(f"  ❌ Designer UI导入失败: {e}")
        return False


def test_ui_files():
    """测试UI文件"""
    print("🔍 测试UI文件...")
    
    ui_files = [
        "src/ui/ui_files/main_window.ui",
        "src/ui/ui_files/furnace_widget.ui",
        "src/ui/ui_files/ui_main_window.py",
        "src/ui/ui_files/ui_furnace_widget.py"
    ]
    
    all_exist = True
    for ui_file in ui_files:
        if Path(ui_file).exists():
            print(f"  ✓ {ui_file}")
        else:
            print(f"  ❌ {ui_file} 不存在")
            all_exist = False
    
    return all_exist


def main():
    """主测试函数"""
    print("UI重构测试")
    print("=" * 50)
    
    # 测试各个组件
    original_ok = test_original_ui()
    designer_ok = test_designer_ui()
    ui_files_ok = test_ui_files()
    
    print("\n📊 测试结果:")
    print(f"  原版UI: {'✅' if original_ok else '❌'}")
    print(f"  Designer UI: {'✅' if designer_ok else '❌'}")
    print(f"  UI文件: {'✅' if ui_files_ok else '❌'}")

    if designer_ok and ui_files_ok:
        print("\n🎉 重构成功! 可以使用Designer UI")
        print("💡 运行命令: python main_designer.py")
    elif original_ok:
        print("\n⚠️  Designer UI未就绪，但原版UI可用")
        print("💡 运行命令: python main.py")
    else:
        print("\n❌ 所有UI都不可用，请检查代码")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open("test_ui_migration.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("  ✓ 创建: test_ui_migration.py")


def main():
    """主迁移函数"""
    print("🔄 UI重构迁移工具")
    print("=" * 50)
    
    try:
        # 执行迁移步骤
        backup_original_files()
        create_migration_main()
        update_imports()
        create_test_script()
        
        print("\n✅ 迁移完成!")
        print("\n📋 下一步操作:")
        print("1. 运行测试: python test_ui_migration.py")
        print("2. 编译UI文件: python src/ui/ui_files/compile_ui.py")
        print("3. 测试新UI: python main_designer.py")
        print("4. 如有问题，可以从backup_original_ui/恢复原文件")

        return 0

    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
