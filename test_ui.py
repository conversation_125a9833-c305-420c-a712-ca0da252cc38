#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI测试脚本 - 快速测试新的界面设计
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ui():
    """测试UI界面"""
    try:
        from PyQt6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        from src.core.task_manager import TaskManager
        from src.data.database_manager import DatabaseManager
        from src.communication.communication_manager import CommunicationManager
        
        print("正在启动UI测试...")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("烧结炉控制系统 - UI测试")
        
        # 初始化组件
        print("初始化数据库...")
        db_manager = DatabaseManager("test_ui.db")
        db_manager.initialize_database()
        
        print("初始化通讯管理器...")
        comm_manager = CommunicationManager()
        
        print("初始化任务管理器...")
        task_manager = TaskManager(db_manager, comm_manager)
        
        print("创建主窗口...")
        main_window = MainWindow(task_manager)
        main_window.show()
        
        print("启动通讯（模拟模式）...")
        comm_manager.start_communication()
        
        print("✓ UI测试启动成功！")
        print("请测试菜单栏的各项功能")
        
        # 运行应用程序
        exit_code = app.exec()
        
        # 清理
        comm_manager.stop_communication()
        task_manager.cleanup()
        
        # 删除测试数据库
        if os.path.exists("test_ui.db"):
            os.remove("test_ui.db")
        
        return exit_code
        
    except Exception as e:
        print(f"UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("烧结炉控制系统 - UI界面测试")
    print("=" * 40)
    
    exit_code = test_ui()
    
    if exit_code == 0:
        print("\n✓ UI测试完成")
    else:
        print("\n✗ UI测试失败")
    
    sys.exit(exit_code)
