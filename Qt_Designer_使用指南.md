# Qt Designer UI重构使用指南

## 🎯 重构完成

您的烧结炉控制系统已成功重构为使用Qt Designer的架构，实现了界面设计与业务逻辑的完全分离。

## 📁 新的项目结构

```
src/ui/
├── ui_files/                    # Qt Designer相关文件
│   ├── main_window.ui          # 主窗口UI设计文件
│   ├── furnace_widget.ui       # 炉子组件UI设计文件
│   ├── compile_ui.py           # UI编译工具
│   ├── ui_main_window.py       # 生成的主窗口UI代码
│   └── ui_furnace_widget.py    # 生成的炉子组件UI代码
├── main_window_new.py          # 重构后的主窗口业务逻辑
├── furnace_widget_new.py       # 重构后的炉子组件业务逻辑
├── ui_compat.py               # UI兼容性文件
└── backup_original_ui/         # 原始文件备份
    ├── main_window.py
    └── furnace_widget.py
```

## 🚀 如何使用

### 1. 使用Qt Designer编辑界面

```bash
# 启动Qt Designer
designer

# 或者通过Python启动
python -m PyQt6.tools.designer
```

**编辑主窗口布局:**
- 打开 `src/ui/ui_files/main_window.ui`
- 可视化编辑菜单、布局、控件
- 保存文件

**编辑炉子组件布局:**
- 打开 `src/ui/ui_files/furnace_widget.ui`
- 调整温度显示、按钮布局等
- 保存文件

### 2. 编译UI文件

每次修改.ui文件后，需要重新编译：

```bash
cd src/ui/ui_files
python compile_ui.py
```

### 3. 运行系统

```bash
# 使用重构后的UI
python main.py
```

## 🔧 开发工作流

### 修改界面布局
1. **Qt Designer** → 编辑.ui文件
2. **编译** → `python compile_ui.py`
3. **测试** → `python main.py`

### 添加新功能
1. **UI设计** → 在.ui文件中添加新控件
2. **编译UI** → 生成新的Python代码
3. **业务逻辑** → 在*_new.py文件中添加处理代码
4. **连接信号** → 连接新控件的信号和槽

### 样式调整
1. **基本样式** → 在Qt Designer中设置
2. **动态样式** → 在业务逻辑代码中实现
3. **高级样式** → 使用QSS样式表

## 💡 核心优势

### ✅ 界面设计与业务逻辑分离
- UI设计师可以独立工作
- 程序员专注业务逻辑
- 代码结构更清晰

### ✅ 可视化设计
- 拖拽式布局
- 实时预览
- 属性面板配置

### ✅ 响应式布局保持
- 完全保持原有的自适应功能
- 智能布局算法
- 动态字体调整

### ✅ 向后兼容
- 保持所有原有功能
- 保持API接口不变
- 可以随时回退到原版

## 🎨 自定义控件

如需添加自定义绘制的控件：

1. **占位符** → 在Designer中放置QWidget
2. **提升控件** → 右键 → "提升为..." → 指定自定义类
3. **实现类** → 编写自定义控件代码

## 📋 常用命令

```bash
# 编译所有UI文件
python src/ui/ui_files/compile_ui.py

# 手动编译单个文件
python -m PyQt6.uic.pyuic main_window.ui -o ui_main_window.py

# 启动Qt Designer
designer
# 或
python -m PyQt6.tools.designer

# 运行系统
python main.py
```

## 🔄 版本切换

如果需要回退到原版UI：

1. **恢复文件**：
   ```bash
   cp backup_original_ui/main_window.py src/ui/
   cp backup_original_ui/furnace_widget.py src/ui/
   ```

2. **修改导入**：
   在main.py中使用原版导入

## 🛠️ 故障排除

### UI编译失败
- 检查PyQt6是否正确安装
- 确保.ui文件格式正确
- 查看错误信息并修复

### 运行时错误
- 检查导入路径
- 确保UI文件已编译
- 查看控制台错误信息

### 布局问题
- 在Qt Designer中检查布局设置
- 确保容器控件正确配置
- 检查尺寸策略设置

## 📈 后续扩展

### 添加新窗口
1. 创建新的.ui文件
2. 编译生成Python代码
3. 创建对应的业务逻辑类
4. 在主窗口中添加调用

### 国际化支持
1. 在Qt Designer中设置文本
2. 使用Qt的翻译系统
3. 生成翻译文件

### 主题支持
1. 创建QSS样式表文件
2. 在代码中动态加载
3. 支持主题切换

## 🎉 总结

重构后的系统具备以下特点：

- ✅ **界面与逻辑分离** - 更好的代码组织
- ✅ **可视化设计** - 提高开发效率
- ✅ **响应式布局** - 保持原有自适应功能
- ✅ **向后兼容** - 平滑迁移
- ✅ **易于维护** - 清晰的文件结构
- ✅ **团队协作** - 设计师和程序员可并行工作

现在您可以享受Qt Designer带来的可视化UI设计体验，同时保持所有原有的功能和性能！
