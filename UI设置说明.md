# 烧结炉控制系统 - UI界面设置说明

## 概述

本系统提供了灵活的界面设置功能，允许用户根据屏幕尺寸和个人喜好自定义所有窗口的大小和位置。

## 🎯 主要功能

### 1. 自适应屏幕尺寸
- 系统会自动检测屏幕分辨率
- 根据屏幕大小推荐合适的窗口尺寸
- 支持从小屏幕(1366x768)到4K大屏的各种分辨率

### 2. 窗口尺寸自定义
- **主窗口**: 系统主界面的尺寸
- **工艺管理窗口**: 工艺库管理界面
- **任务调度窗口**: 任务创建和管理界面
- **任务详情窗口**: 实时温度曲线显示
- **历史查询窗口**: 历史数据查询界面
- **报警窗口**: 报警信息显示界面

### 3. 显示选项
- **窗口居中**: 新窗口是否自动居中显示
- **记住位置**: 是否记住窗口的最后位置
- **自动调整**: 根据屏幕尺寸自动调整窗口大小
- **字体大小**: 界面字体大小设置

## 🛠️ 设置方法

### 方法一：程序内设置
1. 启动烧结炉控制系统
2. 点击菜单栏 "设置" → "界面设置"
3. 在弹出的对话框中调整各项参数
4. 点击"应用"或"确定"保存设置

### 方法二：快速设置脚本
```bash
# 交互式设置
python quick_ui_setup.py

# 查看推荐预设
python quick_ui_setup.py recommend

# 应用指定预设
python quick_ui_setup.py apply "中等屏幕 (1920x1080)"

# 查看当前设置
python quick_ui_setup.py current

# 重置为默认设置
python quick_ui_setup.py reset
```

### 方法三：手动编辑配置文件
编辑 `ui_settings.json` 文件：
```json
{
  "main_window": {"width": 1400, "height": 900},
  "process_manager": {"width": 1200, "height": 800},
  "task_scheduler": {"width": 1300, "height": 850},
  "task_detail": {"width": 1100, "height": 750},
  "history_viewer": {"width": 1350, "height": 800},
  "alarm_window": {"width": 1000, "height": 650},
  "center_windows": true,
  "remember_position": false,
  "auto_resize": true,
  "font_size": "10"
}
```

## 📐 预设配置

### 小屏幕 (1366x768)
适用于笔记本电脑和小尺寸显示器
- 主窗口: 1200x700
- 字体大小: 9
- 紧凑布局，节省屏幕空间

### 中等屏幕 (1920x1080)
适用于标准桌面显示器
- 主窗口: 1400x900
- 字体大小: 10
- 平衡的界面布局

### 大屏幕 (2560x1440)
适用于2K显示器
- 主窗口: 1800x1200
- 字体大小: 11
- 宽敞的界面布局

### 超大屏幕 (4K)
适用于4K显示器
- 主窗口: 2200x1400
- 字体大小: 12
- 充分利用大屏幕空间

## 🔧 高级设置

### 窗口行为选项

#### 窗口居中 (center_windows)
- **启用**: 所有新打开的窗口都会在屏幕中央显示
- **禁用**: 窗口在固定位置打开

#### 记住位置 (remember_position)
- **启用**: 系统会记住每个窗口关闭时的位置，下次打开时恢复
- **禁用**: 窗口总是在默认位置打开

#### 自动调整 (auto_resize)
- **启用**: 窗口尺寸会根据屏幕大小自动调整
- **禁用**: 使用固定的窗口尺寸

### 字体设置
支持的字体大小：8, 9, 10, 11, 12, 14, 16
- 小屏幕推荐: 8-10
- 中等屏幕推荐: 10-12
- 大屏幕推荐: 12-16

## 📱 不同分辨率建议

### 1366x768 (笔记本常见分辨率)
```bash
python quick_ui_setup.py apply "小屏幕 (1366x768)"
```

### 1920x1080 (Full HD)
```bash
python quick_ui_setup.py apply "中等屏幕 (1920x1080)"
```

### 2560x1440 (2K)
```bash
python quick_ui_setup.py apply "大屏幕 (2560x1440)"
```

### 3840x2160 (4K)
```bash
python quick_ui_setup.py apply "超大屏幕 (4K)"
```

## 🚨 故障排除

### 窗口太大无法显示完整
1. 运行快速设置脚本选择合适的预设
2. 或者手动编辑 `ui_settings.json` 减小窗口尺寸

### 窗口太小内容显示不全
1. 选择更大的预设配置
2. 或者在界面设置中增加窗口尺寸

### 字体太小看不清
1. 在界面设置中增加字体大小
2. 或者编辑配置文件中的 `font_size` 值

### 设置不生效
1. 确保配置文件保存成功
2. 重启程序使设置完全生效
3. 检查配置文件格式是否正确

### 恢复默认设置
```bash
python quick_ui_setup.py reset
```
或者删除 `ui_settings.json` 文件

## 💡 使用技巧

1. **首次使用**: 建议先运行快速设置脚本，选择适合的预设
2. **多显示器**: 可以设置窗口不居中，手动调整到合适的显示器
3. **演示模式**: 可以临时调大字体和窗口尺寸，便于演示
4. **开发调试**: 可以设置较小的窗口尺寸，便于同时查看多个窗口

## 📄 配置文件位置

- 配置文件: `ui_settings.json` (程序根目录)
- 快速设置脚本: `quick_ui_setup.py`
- 界面设置对话框: 程序内 "设置" → "界面设置"

## 🔄 更新说明

设置更改后：
- 部分设置立即生效
- 窗口尺寸设置需要重新打开窗口生效
- 字体设置建议重启程序完全生效

---

如有问题，请参考系统日志或联系技术支持。
