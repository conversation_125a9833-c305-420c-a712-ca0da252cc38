#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烧结炉集中控制与数据管理系统
主程序入口
"""

import sys
import os
import traceback
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt


def setup_logging():
    """设置日志记录"""
    log_filename = f"system_{datetime.now().strftime('%Y%m%d')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def show_error_dialog(title, message, details=None):
    """显示错误对话框"""
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        if details:
            msg_box.setDetailedText(details)

        msg_box.exec()
    except Exception as e:
        print(f"无法显示错误对话框: {e}")
        print(f"原始错误: {message}")
        if details:
            print(f"详细信息: {details}")


def check_dependencies():
    """检查依赖包"""
    logger = logging.getLogger(__name__)
    missing_packages = []

    try:
        import PyQt6
        logger.info("✓ PyQt6 已安装")
    except ImportError:
        missing_packages.append("PyQt6")

    try:
        import serial
        logger.info("✓ pyserial 已安装")
    except ImportError:
        missing_packages.append("pyserial")

    try:
        import pandas
        logger.info("✓ pandas 已安装")
    except ImportError:
        missing_packages.append("pandas")

    try:
        import pyqtgraph
        logger.info("✓ pyqtgraph 已安装")
    except ImportError:
        missing_packages.append("pyqtgraph")

    if missing_packages:
        error_msg = f"缺少以下依赖包: {', '.join(missing_packages)}"
        logger.error(error_msg)
        logger.info("请运行: pip install -r requirements.txt")
        return False, error_msg

    return True, "所有依赖包已安装"


def main():
    """主程序入口"""
    logger = setup_logging()
    logger.info("=" * 50)
    logger.info("烧结炉控制系统启动")
    logger.info("=" * 50)

    try:
        # 检查依赖包
        deps_ok, deps_msg = check_dependencies()
        if not deps_ok:
            show_error_dialog("依赖包错误", deps_msg,
                            "请安装所需的依赖包后重试。\n运行命令: pip install -r requirements.txt")
            return 1

        logger.info("开始初始化应用程序...")

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("烧结炉控制系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("工业控制系统")

        # 设置应用程序属性
        app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)

        logger.info("正在初始化数据库...")

        # 初始化数据库
        try:
            from src.data.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            db_manager.initialize_database()
            logger.info("✓ 数据库初始化成功")
        except Exception as e:
            error_msg = f"数据库初始化失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("数据库错误", error_msg, traceback.format_exc())
            return 1

        logger.info("正在初始化通讯管理器...")

        # 初始化通讯管理器
        try:
            from src.communication.communication_manager import CommunicationManager
            comm_manager = CommunicationManager()
            logger.info("✓ 通讯管理器初始化成功")
        except Exception as e:
            error_msg = f"通讯管理器初始化失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("通讯错误", error_msg, traceback.format_exc())
            return 1

        logger.info("正在初始化任务管理器...")

        # 初始化任务管理器
        try:
            from src.core.task_manager import TaskManager
            task_manager = TaskManager(db_manager, comm_manager)
            logger.info("✓ 任务管理器初始化成功")
        except Exception as e:
            error_msg = f"任务管理器初始化失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("任务管理器错误", error_msg, traceback.format_exc())
            return 1

        logger.info("正在创建主窗口...")

        # 创建主窗口
        try:
            from src.ui.main_window import MainWindow
            main_window = MainWindow(task_manager)
            main_window.show()
            logger.info("✓ 主窗口创建成功")
        except Exception as e:
            error_msg = f"主窗口创建失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("界面错误", error_msg, traceback.format_exc())
            return 1

        logger.info("正在启动通讯...")

        # 启动通讯（可选，如果没有串口设备也能运行）
        try:
            comm_manager.start_communication()
            logger.info("✓ 通讯启动成功")
        except Exception as e:
            logger.warning(f"通讯启动失败: {str(e)}")
            logger.warning("系统将在模拟模式下运行")

        logger.info("系统启动完成，进入主循环...")

        # 运行应用程序
        try:
            exit_code = app.exec()
            logger.info(f"应用程序正常退出，退出码: {exit_code}")
            return exit_code
        except Exception as e:
            error_msg = f"应用程序运行时错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("运行时错误", error_msg, traceback.format_exc())
            return 1

    except Exception as e:
        error_msg = f"系统启动失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        show_error_dialog("启动错误", error_msg, traceback.format_exc())
        return 1

    finally:
        # 清理资源
        try:
            logger.info("正在清理资源...")
            if 'comm_manager' in locals():
                comm_manager.stop_communication()
                logger.info("✓ 通讯管理器已停止")
            if 'task_manager' in locals():
                task_manager.cleanup()
                logger.info("✓ 任务管理器已清理")
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {str(e)}")


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
