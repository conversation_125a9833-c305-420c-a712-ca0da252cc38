#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI配置文件 - 统一管理所有窗口的尺寸和位置
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QRect
import json
import os


class UIConfig:
    """UI配置类"""

    _user_settings = None

    @classmethod
    def load_user_settings(cls):
        """加载用户设置"""
        if cls._user_settings is None:
            settings_file = "ui_settings.json"
            default_settings = {
                "main_window": {"width": 1400, "height": 900},
                "process_manager": {"width": 1200, "height": 800},
                "task_scheduler": {"width": 1300, "height": 850},
                "task_detail": {"width": 1100, "height": 750},
                "history_viewer": {"width": 1350, "height": 800},
                "alarm_window": {"width": 1000, "height": 650},
                "center_windows": True,
                "remember_position": False,
                "auto_resize": True,
                "font_size": "10"
            }

            if os.path.exists(settings_file):
                try:
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        user_settings = json.load(f)
                        default_settings.update(user_settings)
                except Exception as e:
                    print(f"加载用户设置失败: {e}")

            cls._user_settings = default_settings

        return cls._user_settings

    @staticmethod
    def get_screen_size():
        """获取屏幕尺寸"""
        screen = QApplication.primaryScreen()
        if screen:
            return screen.availableGeometry()
        return QRect(0, 0, 1920, 1080)  # 默认尺寸
    
    @staticmethod
    def center_window(width, height, offset_x=0, offset_y=0):
        """计算窗口居中位置"""
        screen_rect = UIConfig.get_screen_size()
        x = (screen_rect.width() - width) // 2 + offset_x
        y = (screen_rect.height() - height) // 2 + offset_y
        return x, y
    
    @classmethod
    def get_main_window_geometry(cls):
        """主窗口几何设置"""
        settings = cls.load_user_settings()
        screen_rect = cls.get_screen_size()

        # 使用用户设置或默认值
        if settings["auto_resize"]:
            width = min(int(screen_rect.width() * 0.8), settings["main_window"]["width"])
            height = min(int(screen_rect.height() * 0.8), settings["main_window"]["height"])
        else:
            width = settings["main_window"]["width"]
            height = settings["main_window"]["height"]

        if settings["center_windows"]:
            x, y = cls.center_window(width, height)
        else:
            x, y = 100, 100

        return x, y, width, height
    
    @classmethod
    def get_process_manager_geometry(cls):
        """工艺管理窗口几何设置"""
        settings = cls.load_user_settings()

        width = settings["process_manager"]["width"]
        height = settings["process_manager"]["height"]

        if settings["center_windows"]:
            x, y = cls.center_window(width, height, -50, -50)
        else:
            x, y = 150, 150

        return x, y, width, height

    @classmethod
    def get_task_scheduler_geometry(cls):
        """任务调度窗口几何设置"""
        settings = cls.load_user_settings()

        width = settings["task_scheduler"]["width"]
        height = settings["task_scheduler"]["height"]

        if settings["center_windows"]:
            x, y = cls.center_window(width, height, 0, -30)
        else:
            x, y = 100, 100

        return x, y, width, height

    @classmethod
    def get_task_detail_geometry(cls):
        """任务详情窗口几何设置"""
        settings = cls.load_user_settings()

        width = settings["task_detail"]["width"]
        height = settings["task_detail"]["height"]

        if settings["center_windows"]:
            x, y = cls.center_window(width, height, 50, 50)
        else:
            x, y = 200, 200

        return x, y, width, height

    @classmethod
    def get_history_viewer_geometry(cls):
        """历史查询窗口几何设置"""
        settings = cls.load_user_settings()

        width = settings["history_viewer"]["width"]
        height = settings["history_viewer"]["height"]

        if settings["center_windows"]:
            x, y = cls.center_window(width, height, -30, 0)
        else:
            x, y = 100, 100

        return x, y, width, height

    @classmethod
    def get_alarm_window_geometry(cls):
        """报警窗口几何设置"""
        settings = cls.load_user_settings()

        width = settings["alarm_window"]["width"]
        height = settings["alarm_window"]["height"]

        if settings["center_windows"]:
            x, y = cls.center_window(width, height, 30, -20)
        else:
            x, y = 100, 100

        return x, y, width, height
    
    @staticmethod
    def get_dialog_geometry(dialog_type="default"):
        """对话框几何设置"""
        screen_rect = UIConfig.get_screen_size()
        
        if dialog_type == "process_edit":
            # 工艺编辑对话框
            width = min(int(screen_rect.width() * 0.4), 600)
            height = min(int(screen_rect.height() * 0.5), 500)
        elif dialog_type == "step_edit":
            # 步骤编辑对话框
            width = min(int(screen_rect.width() * 0.3), 400)
            height = min(int(screen_rect.height() * 0.3), 300)
        elif dialog_type == "task_create":
            # 任务创建对话框
            width = min(int(screen_rect.width() * 0.35), 500)
            height = min(int(screen_rect.height() * 0.4), 400)
        else:
            # 默认对话框
            width = min(int(screen_rect.width() * 0.25), 350)
            height = min(int(screen_rect.height() * 0.25), 250)
        
        x, y = UIConfig.center_window(width, height)
        return x, y, width, height
    
    # 字体配置
    TITLE_FONT_SIZE = 16
    SUBTITLE_FONT_SIZE = 12
    NORMAL_FONT_SIZE = 10
    SMALL_FONT_SIZE = 9
    
    # 颜色配置
    COLORS = {
        'working': '#4CAF50',      # 绿色 - 工作中
        'idle': '#2196F3',         # 蓝色 - 空闲
        'error': '#F44336',        # 红色 - 故障
        'comm_error': '#9E9E9E',   # 灰色 - 通讯中断
        'warning': '#FF9800',      # 橙色 - 警告
        'background': '#FAFAFA',   # 背景色
        'border': '#E0E0E0',       # 边框色
    }
    
    # 间距配置
    SPACING = {
        'small': 5,
        'medium': 10,
        'large': 15,
        'xlarge': 20,
    }
    
    # 表格配置
    TABLE_CONFIG = {
        'row_height': 30,
        'header_height': 35,
        'min_column_width': 80,
        'max_column_width': 300,
    }


def apply_window_geometry(window, geometry_func):
    """应用窗口几何设置的辅助函数"""
    x, y, width, height = geometry_func()
    window.setGeometry(x, y, width, height)
    
    # 设置最小尺寸为当前尺寸的70%
    min_width = int(width * 0.7)
    min_height = int(height * 0.7)
    window.setMinimumSize(min_width, min_height)


def apply_dialog_geometry(dialog, dialog_type="default"):
    """应用对话框几何设置的辅助函数"""
    x, y, width, height = UIConfig.get_dialog_geometry(dialog_type)
    dialog.setGeometry(x, y, width, height)
    
    # 对话框通常设置固定大小
    dialog.setFixedSize(width, height)
