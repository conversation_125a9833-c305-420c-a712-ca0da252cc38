#!/bin/bash

echo "烧结炉集中控制与数据管理系统"
echo "================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

echo "检查依赖包..."
python3 -c "import PyQt6" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: PyQt6未安装，正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

echo "启动系统..."
python3 main.py

if [ $? -ne 0 ]; then
    echo
    echo "系统启动失败，请检查错误信息"
    read -p "按任意键继续..."
fi
