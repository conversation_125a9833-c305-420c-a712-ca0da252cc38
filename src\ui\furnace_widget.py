#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炉子监控组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QFont, QPalette, QColor
from datetime import datetime

from ..models.furnace import FurnaceStatus, FurnaceData
from ..core.task_manager import TaskManager


class FurnaceWidget(QFrame):
    """炉子监控组件"""
    
    def __init__(self, furnace_id: int, task_manager: TaskManager):
        super().__init__()
        
        self.furnace_id = furnace_id
        self.task_manager = task_manager
        self.task_detail_window = None
        self.has_alarm = False
        
        self.init_ui()
        self.update_display()
    
    def init_ui(self):
        """初始化用户界面"""
        # 移除固定尺寸，改为设置最小尺寸和尺寸策略
        self.setMinimumSize(200, 150)
        self.setMaximumSize(400, 300)
        self.setFrameStyle(QFrame.Shape.Box)

        layout = QVBoxLayout(self)
        layout.setSpacing(3)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 炉子编号标题
        self.title_label = QLabel(f"{self.furnace_id}号炉")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(12)  # 减小字体以适应小尺寸
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        layout.addWidget(self.title_label)

        # 状态显示
        self.status_label = QLabel("状态: 通讯中断")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)  # 允许文字换行
        status_font = QFont()
        status_font.setPointSize(10)  # 减小字体
        status_font.setBold(True)
        self.status_label.setFont(status_font)
        layout.addWidget(self.status_label)
        
        # 温度信息
        temp_layout = QHBoxLayout()
        temp_layout.setSpacing(5)

        # 当前温度
        pv_layout = QVBoxLayout()
        pv_layout.setSpacing(2)
        pv_title = QLabel("当前温度(PV)")
        pv_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pv_title_font = QFont()
        pv_title_font.setPointSize(8)
        pv_title.setFont(pv_title_font)
        pv_layout.addWidget(pv_title)

        self.pv_label = QLabel("0.0°C")
        self.pv_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pv_font = QFont()
        pv_font.setPointSize(10)
        pv_font.setBold(True)
        self.pv_label.setFont(pv_font)
        pv_layout.addWidget(self.pv_label)
        temp_layout.addLayout(pv_layout)

        # 设定温度
        sv_layout = QVBoxLayout()
        sv_layout.setSpacing(2)
        sv_title = QLabel("设定温度(SV)")
        sv_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sv_title_font = QFont()
        sv_title_font.setPointSize(8)
        sv_title.setFont(sv_title_font)
        sv_layout.addWidget(sv_title)

        self.sv_label = QLabel("0.0°C")
        self.sv_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sv_font = QFont()
        sv_font.setPointSize(10)
        sv_font.setBold(True)
        self.sv_label.setFont(sv_font)
        sv_layout.addWidget(self.sv_label)
        temp_layout.addLayout(sv_layout)

        layout.addLayout(temp_layout)
        
        # 任务时间信息
        self.time_info_label = QLabel("空闲中")
        self.time_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_info_label.setWordWrap(True)
        time_font = QFont()
        time_font.setPointSize(8)
        self.time_info_label.setFont(time_font)
        layout.addWidget(self.time_info_label)

        # 控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(3)

        self.detail_btn = QPushButton("详情")
        self.detail_btn.clicked.connect(self.show_detail)
        self.detail_btn.setMaximumHeight(25)  # 限制按钮高度
        button_layout.addWidget(self.detail_btn)

        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_task)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setMaximumHeight(25)  # 限制按钮高度
        button_layout.addWidget(self.stop_btn)

        layout.addLayout(button_layout)
        
        # 设置默认样式
        self.set_status_style(FurnaceStatus.COMM_ERROR)
    
    def update_data(self, furnace_data: FurnaceData):
        """更新炉子数据"""
        try:
            # 更新温度显示
            self.pv_label.setText(f"{furnace_data.current_temp:.1f}°C")
            self.sv_label.setText(f"{furnace_data.set_temp:.1f}°C")
            
            # 更新状态
            self.set_status_style(furnace_data.status)
            
            # 更新时间信息
            self.update_time_info(furnace_data)
            
            # 更新按钮状态
            self.update_button_states(furnace_data.status)
            
        except Exception as e:
            print(f"更新炉子{self.furnace_id}数据失败: {e}")
    
    def update_time_info(self, furnace_data: FurnaceData):
        """更新时间信息"""
        try:
            if furnace_data.status == FurnaceStatus.WORKING:
                if furnace_data.task_start_time:
                    start_str = furnace_data.task_start_time.strftime("%H:%M:%S")
                    if furnace_data.task_end_time:
                        end_str = furnace_data.task_end_time.strftime("%H:%M:%S")
                        self.time_info_label.setText(f"开始: {start_str}\n预计结束: {end_str}")
                    else:
                        self.time_info_label.setText(f"开始时间: {start_str}")
                else:
                    self.time_info_label.setText("工作中")
            elif furnace_data.status == FurnaceStatus.IDLE:
                if furnace_data.next_task_time:
                    next_str = furnace_data.next_task_time.strftime("%H:%M:%S")
                    self.time_info_label.setText(f"下次任务: {next_str}")
                else:
                    self.time_info_label.setText("空闲中")
            elif furnace_data.status == FurnaceStatus.FAULT:
                self.time_info_label.setText("设备故障")
            elif furnace_data.status == FurnaceStatus.COMM_ERROR:
                self.time_info_label.setText("通讯中断")
                
        except Exception as e:
            print(f"更新时间信息失败: {e}")
    
    def set_status_style(self, status: FurnaceStatus):
        """设置状态样式"""
        try:
            status_text = f"状态: {status.value}"
            self.status_label.setText(status_text)
            
            # 根据状态设置颜色
            if status == FurnaceStatus.WORKING:
                color = "green"
            elif status == FurnaceStatus.IDLE:
                color = "blue"
            elif status == FurnaceStatus.FAULT:
                color = "red"
            elif status == FurnaceStatus.COMM_ERROR:
                color = "gray"
            else:
                color = "black"
            
            # 如果有报警，使用红色背景
            if self.has_alarm:
                self.setStyleSheet(f"""
                    QWidget {{
                        background-color: #ffcccc;
                        border: 2px solid red;
                    }}
                    QLabel {{
                        color: {color};
                    }}
                """)
            else:
                self.setStyleSheet(f"""
                    QWidget {{
                        background-color: white;
                        border: 1px solid gray;
                    }}
                    QLabel {{
                        color: {color};
                    }}
                """)
                
        except Exception as e:
            print(f"设置状态样式失败: {e}")
    
    def set_alarm_state(self, has_alarm: bool):
        """设置报警状态"""
        self.has_alarm = has_alarm
        # 重新应用样式
        furnace_data = self.task_manager.comm_manager.get_furnace_data(self.furnace_id)
        if furnace_data:
            self.set_status_style(furnace_data.status)
    
    def update_button_states(self, status: FurnaceStatus):
        """更新按钮状态"""
        try:
            # 详情按钮始终可用
            self.detail_btn.setEnabled(True)
            
            # 停止按钮只在工作状态下可用
            is_working = (status == FurnaceStatus.WORKING and 
                         self.furnace_id in self.task_manager.get_running_tasks())
            self.stop_btn.setEnabled(is_working)
            
        except Exception as e:
            print(f"更新按钮状态失败: {e}")
    
    def update_display(self):
        """更新显示"""
        try:
            furnace_data = self.task_manager.comm_manager.get_furnace_data(self.furnace_id)
            if furnace_data:
                self.update_data(furnace_data)
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    @pyqtSlot()
    def show_detail(self):
        """显示详情"""
        try:
            # 延迟导入避免循环导入
            from .task_detail_window import TaskDetailWindow

            if not self.task_detail_window:
                self.task_detail_window = TaskDetailWindow(self.furnace_id, self.task_manager)

            self.task_detail_window.show()
            self.task_detail_window.raise_()

        except Exception as e:
            print(f"显示详情失败: {e}")
            QMessageBox.warning(self, "错误", f"显示详情失败: {e}")
    
    @pyqtSlot()
    def stop_task(self):
        """停止任务"""
        try:
            reply = QMessageBox.question(self, '确认停止', 
                                       f'确定要停止{self.furnace_id}号炉的任务吗？',
                                       QMessageBox.StandardButton.Yes | 
                                       QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                success = self.task_manager.stop_task(self.furnace_id)
                if success:
                    QMessageBox.information(self, "成功", "任务已停止")
                else:
                    QMessageBox.warning(self, "失败", "停止任务失败")
                    
        except Exception as e:
            print(f"停止任务失败: {e}")
            QMessageBox.warning(self, "错误", f"停止任务失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.show_detail()
        super().mousePressEvent(event)
