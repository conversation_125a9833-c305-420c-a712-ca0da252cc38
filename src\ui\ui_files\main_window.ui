<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>烧结炉集中控制与数据管理系统</string>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>600</height>
   </size>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="main_layout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QScrollArea" name="scroll_area">
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <property name="horizontalScrollBarPolicy">
       <enum>Qt::ScrollBarAsNeeded</enum>
      </property>
      <property name="verticalScrollBarPolicy">
       <enum>Qt::ScrollBarAsNeeded</enum>
      </property>
      <widget class="QWidget" name="scroll_area_contents">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>1398</width>
         <height>847</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="container_layout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <spacer name="left_spacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QFrame" name="furnace_frame">
          <property name="frameShape">
           <enum>QFrame::Box</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QGridLayout" name="furnace_layout">
           <property name="spacing">
            <number>10</number>
           </property>
           <property name="leftMargin">
            <number>15</number>
           </property>
           <property name="topMargin">
            <number>15</number>
           </property>
           <property name="rightMargin">
            <number>15</number>
           </property>
           <property name="bottomMargin">
            <number>15</number>
           </property>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="right_spacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1400</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu_file">
    <property name="title">
     <string>文件</string>
    </property>
   </widget>
   <widget class="QMenu" name="menu_process">
    <property name="title">
     <string>工艺</string>
    </property>
   </widget>
   <widget class="QMenu" name="menu_task">
    <property name="title">
     <string>任务</string>
    </property>
   </widget>
   <widget class="QMenu" name="menu_query">
    <property name="title">
     <string>查询</string>
    </property>
   </widget>
   <widget class="QMenu" name="menu_tools">
    <property name="title">
     <string>工具</string>
    </property>
   </widget>
   <widget class="QMenu" name="menu_help">
    <property name="title">
     <string>帮助</string>
    </property>
   </widget>
   <addaction name="menu_file"/>
   <addaction name="menu_process"/>
   <addaction name="menu_task"/>
   <addaction name="menu_query"/>
   <addaction name="menu_tools"/>
   <addaction name="menu_help"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
