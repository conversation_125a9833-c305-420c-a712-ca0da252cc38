#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工艺库管理窗口
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox,
                            QGroupBox, QGridLayout, QMessageBox, QHeaderView,
                            QSplitter, QWidget, QAbstractItemView)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QFont

from ..core.task_manager import TaskManager
from ..models.process import Process, ProcessStep
from .ui_config import UIConfig, apply_window_geometry


class ProcessManagerWindow(QDialog):
    """工艺库管理窗口"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        self.task_manager = task_manager
        self.current_process = None
        
        self.init_ui()
        self.load_processes()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("工艺库管理")

        # 使用配置文件设置窗口几何
        apply_window_geometry(self, UIConfig.get_process_manager_geometry)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("工艺库管理")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧工艺列表
        list_widget = self.create_process_list()
        splitter.addWidget(list_widget)
        
        # 右侧工艺编辑
        edit_widget = self.create_process_editor()
        splitter.addWidget(edit_widget)
        
        # 设置分割比例
        splitter.setSizes([400, 600])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.new_btn = QPushButton("新建工艺")
        self.new_btn.clicked.connect(self.new_process)
        button_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton("保存工艺")
        self.save_btn.clicked.connect(self.save_process)
        button_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton("删除工艺")
        self.delete_btn.clicked.connect(self.delete_process)
        button_layout.addWidget(self.delete_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_process_list(self):
        """创建工艺列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 工艺列表标题
        list_label = QLabel("工艺列表")
        list_label.setFont(QFont("", 12, QFont.Weight.Bold))
        layout.addWidget(list_label)
        
        # 工艺列表表格
        self.process_table = QTableWidget()
        self.process_table.setColumnCount(3)
        self.process_table.setHorizontalHeaderLabels(["工艺名称", "步骤数", "创建时间"])
        
        # 设置表格属性
        self.process_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.process_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.process_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.process_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        # 连接选择信号
        self.process_table.itemSelectionChanged.connect(self.on_process_selected)
        
        layout.addWidget(self.process_table)
        
        return widget
    
    def create_process_editor(self):
        """创建工艺编辑器"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout(basic_group)
        
        basic_layout.addWidget(QLabel("工艺名称:"), 0, 0)
        self.name_edit = QLineEdit()
        basic_layout.addWidget(self.name_edit, 0, 1)
        
        basic_layout.addWidget(QLabel("工艺描述:"), 1, 0)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        basic_layout.addWidget(self.description_edit, 1, 1)
        
        layout.addWidget(basic_group)
        
        # 工艺步骤组
        steps_group = QGroupBox("工艺步骤")
        steps_layout = QVBoxLayout(steps_group)
        
        # 步骤操作按钮
        step_button_layout = QHBoxLayout()
        
        self.add_step_btn = QPushButton("添加步骤")
        self.add_step_btn.clicked.connect(self.add_step)
        step_button_layout.addWidget(self.add_step_btn)
        
        self.remove_step_btn = QPushButton("删除步骤")
        self.remove_step_btn.clicked.connect(self.remove_step)
        step_button_layout.addWidget(self.remove_step_btn)
        
        step_button_layout.addStretch()
        
        steps_layout.addLayout(step_button_layout)
        
        # 步骤表格
        self.steps_table = QTableWidget()
        self.steps_table.setColumnCount(3)
        self.steps_table.setHorizontalHeaderLabels(["步骤", "目标温度(°C)", "升温速率(°C/min)"])
        
        # 设置表格属性
        self.steps_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.steps_table.setAlternatingRowColors(True)
        
        # 设置列宽
        steps_header = self.steps_table.horizontalHeader()
        steps_header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        steps_header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        steps_header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        
        steps_layout.addWidget(self.steps_table)
        
        layout.addWidget(steps_group)
        
        # 新步骤输入组
        new_step_group = QGroupBox("新增步骤")
        new_step_layout = QGridLayout(new_step_group)
        
        new_step_layout.addWidget(QLabel("目标温度(°C):"), 0, 0)
        self.target_temp_spin = QDoubleSpinBox()
        self.target_temp_spin.setRange(0, 1200)
        self.target_temp_spin.setValue(100)
        new_step_layout.addWidget(self.target_temp_spin, 0, 1)
        
        new_step_layout.addWidget(QLabel("升温速率(°C/min):"), 0, 2)
        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setRange(0.1, 100)
        self.rate_spin.setValue(5.0)
        new_step_layout.addWidget(self.rate_spin, 0, 3)
        
        layout.addWidget(new_step_group)
        
        return widget
    
    def load_processes(self):
        """加载工艺列表"""
        try:
            processes = self.task_manager.db_manager.get_all_processes()
            
            self.process_table.setRowCount(len(processes))
            
            for row, process in enumerate(processes):
                # 工艺名称
                name_item = QTableWidgetItem(process.name)
                name_item.setData(Qt.ItemDataRole.UserRole, process.process_id)
                self.process_table.setItem(row, 0, name_item)
                
                # 步骤数
                steps_item = QTableWidgetItem(str(len(process.steps)))
                self.process_table.setItem(row, 1, steps_item)
                
                # 创建时间
                time_item = QTableWidgetItem(process.created_time.strftime("%Y-%m-%d %H:%M"))
                self.process_table.setItem(row, 2, time_item)
            
        except Exception as e:
            print(f"加载工艺列表失败: {e}")
            QMessageBox.warning(self, "错误", f"加载工艺列表失败: {e}")
    
    @pyqtSlot()
    def on_process_selected(self):
        """工艺选择事件"""
        try:
            current_row = self.process_table.currentRow()
            if current_row >= 0:
                name_item = self.process_table.item(current_row, 0)
                if name_item:
                    process_id = name_item.data(Qt.ItemDataRole.UserRole)
                    self.load_process(process_id)
            
        except Exception as e:
            print(f"选择工艺失败: {e}")
    
    def load_process(self, process_id: str):
        """加载工艺详情"""
        try:
            process = self.task_manager.db_manager.get_process(process_id)
            if not process:
                return
            
            self.current_process = process
            
            # 加载基本信息
            self.name_edit.setText(process.name)
            self.description_edit.setText(process.description or "")
            
            # 加载工艺步骤
            self.load_process_steps()
            
        except Exception as e:
            print(f"加载工艺详情失败: {e}")
    
    def load_process_steps(self):
        """加载工艺步骤"""
        try:
            if not self.current_process:
                self.steps_table.setRowCount(0)
                return
            
            steps = self.current_process.steps
            self.steps_table.setRowCount(len(steps))
            
            for row, step in enumerate(steps):
                # 步骤号
                step_item = QTableWidgetItem(f"步骤{step.step_id}")
                self.steps_table.setItem(row, 0, step_item)
                
                # 目标温度
                temp_item = QTableWidgetItem(f"{step.target_temp:.1f}")
                self.steps_table.setItem(row, 1, temp_item)
                
                # 升温速率
                rate_item = QTableWidgetItem(f"{step.rate:.1f}")
                self.steps_table.setItem(row, 2, rate_item)
            
        except Exception as e:
            print(f"加载工艺步骤失败: {e}")
    
    @pyqtSlot()
    def new_process(self):
        """新建工艺"""
        try:
            self.current_process = Process(
                process_id=None,
                name="新工艺",
                description=""
            )
            
            self.name_edit.setText("新工艺")
            self.description_edit.setText("")
            self.load_process_steps()
            
        except Exception as e:
            print(f"新建工艺失败: {e}")
    
    @pyqtSlot()
    def save_process(self):
        """保存工艺"""
        try:
            if not self.current_process:
                QMessageBox.warning(self, "警告", "请先选择或新建一个工艺")
                return
            
            # 验证输入
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "请输入工艺名称")
                return
            
            if len(self.current_process.steps) == 0:
                QMessageBox.warning(self, "警告", "请至少添加一个工艺步骤")
                return
            
            # 更新工艺信息
            self.current_process.name = name
            self.current_process.description = self.description_edit.toPlainText().strip()
            
            # 保存到数据库
            process_id = self.task_manager.db_manager.save_process(self.current_process)
            self.current_process.process_id = process_id
            
            # 刷新列表
            self.load_processes()
            
            QMessageBox.information(self, "成功", "工艺保存成功")
            
        except Exception as e:
            print(f"保存工艺失败: {e}")
            QMessageBox.warning(self, "错误", f"保存工艺失败: {e}")
    
    @pyqtSlot()
    def delete_process(self):
        """删除工艺"""
        try:
            if not self.current_process or not self.current_process.process_id:
                QMessageBox.warning(self, "警告", "请先选择一个工艺")
                return
            
            reply = QMessageBox.question(self, '确认删除', 
                                       f'确定要删除工艺"{self.current_process.name}"吗？',
                                       QMessageBox.StandardButton.Yes | 
                                       QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                success = self.task_manager.db_manager.delete_process(self.current_process.process_id)
                if success:
                    self.load_processes()
                    self.current_process = None
                    self.name_edit.clear()
                    self.description_edit.clear()
                    self.load_process_steps()
                    QMessageBox.information(self, "成功", "工艺删除成功")
                else:
                    QMessageBox.warning(self, "失败", "删除失败，可能有任务正在使用此工艺")
            
        except Exception as e:
            print(f"删除工艺失败: {e}")
            QMessageBox.warning(self, "错误", f"删除工艺失败: {e}")
    
    @pyqtSlot()
    def add_step(self):
        """添加步骤"""
        try:
            if not self.current_process:
                QMessageBox.warning(self, "警告", "请先选择或新建一个工艺")
                return
            
            target_temp = self.target_temp_spin.value()
            rate = self.rate_spin.value()
            
            step = self.current_process.add_step(target_temp, rate)
            self.load_process_steps()
            
        except Exception as e:
            print(f"添加步骤失败: {e}")
    
    @pyqtSlot()
    def remove_step(self):
        """删除步骤"""
        try:
            if not self.current_process:
                return
            
            current_row = self.steps_table.currentRow()
            if current_row >= 0 and current_row < len(self.current_process.steps):
                step_id = current_row + 1  # 步骤ID从1开始
                self.current_process.remove_step(step_id)
                self.load_process_steps()
            
        except Exception as e:
            print(f"删除步骤失败: {e}")
