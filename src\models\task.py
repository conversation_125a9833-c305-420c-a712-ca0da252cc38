#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务数据模型
"""

from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, List


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "待执行"
    RUNNING = "执行中"
    COMPLETED = "已完成"
    STOPPED = "中途停止"
    FAILED = "执行失败"


@dataclass
class TemperatureRecord:
    """温度记录"""
    timestamp: datetime
    set_temp: float
    actual_temp: float
    step_id: int = 0


@dataclass
class Task:
    """烧结任务"""
    task_id: Optional[str]
    process_id: str
    furnace_id: int
    status: TaskStatus
    scheduled_start_time: datetime
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    created_time: datetime = field(default_factory=datetime.now)
    temperature_records: List[TemperatureRecord] = field(default_factory=list)
    current_step: int = 0
    
    def add_temperature_record(self, set_temp: float, actual_temp: float, step_id: int = 0):
        """添加温度记录"""
        record = TemperatureRecord(
            timestamp=datetime.now(),
            set_temp=set_temp,
            actual_temp=actual_temp,
            step_id=step_id
        )
        self.temperature_records.append(record)
    
    def start_task(self):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.actual_start_time = datetime.now()
    
    def complete_task(self):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.actual_end_time = datetime.now()
    
    def stop_task(self):
        """停止任务"""
        self.status = TaskStatus.STOPPED
        self.actual_end_time = datetime.now()
    
    def fail_task(self):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.actual_end_time = datetime.now()
    
    def get_duration(self) -> Optional[int]:
        """获取任务执行时长（分钟）"""
        if self.actual_start_time and self.actual_end_time:
            delta = self.actual_end_time - self.actual_start_time
            return int(delta.total_seconds() / 60)
        return None
