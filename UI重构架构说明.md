# UI重构架构说明 - Qt Designer分离方案

## 🎯 重构目标

实现界面设计与业务逻辑的完全分离，使用Qt Designer进行可视化UI设计，提高开发效率和代码可维护性。

## 📁 新的文件结构

```
src/ui/
├── ui_files/                    # Qt Designer UI文件目录
│   ├── main_window.ui          # 主窗口UI设计文件
│   ├── furnace_widget.ui       # 炉子组件UI设计文件
│   ├── compile_ui.py           # UI编译工具
│   ├── ui_main_window.py       # 生成的主窗口UI代码
│   └── ui_furnace_widget.py    # 生成的炉子组件UI代码
├── main_window_new.py          # 重构后的主窗口业务逻辑
├── furnace_widget_new.py       # 重构后的炉子组件业务逻辑
└── ... (其他原有文件)
```

## 🔧 架构设计

### 1. UI设计层 (.ui文件)
- **main_window.ui**: 主窗口布局设计
  - 菜单栏结构
  - 滚动区域配置
  - 炉子组件容器布局
  - 状态栏设置

- **furnace_widget.ui**: 炉子组件布局设计
  - 标题标签
  - 状态显示
  - 温度显示区域
  - 按钮布局

### 2. UI代码生成层 (ui_*.py文件)
- 由pyuic6自动生成
- 包含Ui_类定义和setupUi方法
- **不要手动修改**这些文件

### 3. 业务逻辑层 (*_new.py文件)
- **MainWindow**: 继承QMainWindow，使用Ui_MainWindow
- **FurnaceWidget**: 继承QFrame，使用Ui_FurnaceWidget
- 包含所有业务逻辑和事件处理

## 🛠️ 开发工作流

### 1. UI设计阶段
```bash
# 使用Qt Designer编辑UI文件
designer src/ui/ui_files/main_window.ui
designer src/ui/ui_files/furnace_widget.ui
```

### 2. 代码生成阶段
```bash
# 编译UI文件为Python代码
cd src/ui/ui_files
python compile_ui.py
```

### 3. 业务逻辑开发
- 在*_new.py文件中实现业务逻辑
- 通过self.ui访问UI元素
- 连接信号和槽

## 💡 核心优势

### 1. 界面设计与业务逻辑分离
```python
class MainWindow(QMainWindow):
    def __init__(self, task_manager):
        super().__init__()
        
        # 设置UI (由Designer设计)
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        # 业务逻辑初始化
        self.task_manager = task_manager
        self.init_business_logic()
```

### 2. 可视化设计
- 拖拽式布局设计
- 实时预览效果
- 属性面板配置
- 样式表编辑

### 3. 代码自动生成
```python
# 自动生成的UI代码
class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        # 自动生成的布局代码
        self.centralwidget = QtWidgets.QWidget(parent=MainWindow)
        self.main_layout = QtWidgets.QVBoxLayout(self.centralwidget)
        # ...
```

### 4. 团队协作友好
- UI设计师可以独立工作
- 程序员专注业务逻辑
- 版本控制更清晰

## 🔄 响应式布局保持

重构后的代码完全保持了原有的响应式布局功能：

### 智能布局算法
```python
def arrange_furnace_widgets(self):
    """根据窗口大小安排炉子组件布局"""
    # 计算最佳行列组合
    options = [(1, 8), (2, 4), (3, 3), (4, 2)]
    # 选择最适合的布局
    # 动态调整组件尺寸
```

### 自适应字体
```python
def adjust_font_sizes(self):
    """根据组件大小调整字体"""
    widget_width = self.width()
    if widget_width > 400:
        title_size = 16
    elif widget_width > 300:
        title_size = 14
    else:
        title_size = 12
```

## 📋 使用指南

### 1. 修改UI布局
1. 用Qt Designer打开对应的.ui文件
2. 进行可视化编辑
3. 保存.ui文件
4. 运行compile_ui.py重新生成Python代码

### 2. 添加新功能
1. 在.ui文件中添加新控件
2. 重新编译UI
3. 在业务逻辑类中添加对应的处理代码

### 3. 样式调整
1. 在Qt Designer中设置基本样式
2. 在业务逻辑类中添加动态样式代码
3. 使用QSS文件进行高级样式定制

## 🔧 工具和命令

### UI编译工具
```bash
# 编译所有UI文件
python src/ui/ui_files/compile_ui.py

# 手动编译单个文件
python -m PyQt6.uic.pyuic main_window.ui -o ui_main_window.py
```

### Qt Designer启动
```bash
# Windows
designer

# 或者通过Python
python -m PyQt6.tools.designer
```

## 🎨 自定义控件支持

对于需要自定义绘制的控件：

1. 在Designer中放置QWidget占位符
2. 使用"提升为(Promote to...)"功能
3. 指定自定义控件类名
4. 在代码中实现自定义控件

## 📈 迁移建议

### 渐进式迁移
1. 保留原有代码作为备份
2. 逐个组件进行重构
3. 充分测试每个重构的组件
4. 最后替换主入口

### 兼容性保证
- 保持所有公共接口不变
- 保持信号和槽的兼容性
- 保持配置文件格式不变

这种重构方案实现了真正的界面设计与业务逻辑分离，提高了代码的可维护性和开发效率，同时保持了所有原有功能。
