[communication]
# 串口配置
port = COM1
baudrate = 9600
timeout = 1.0
poll_interval = 2.0

# 设备地址映射 (炉子ID:设备地址)
furnace_1_address = 1
furnace_2_address = 2
furnace_3_address = 3
furnace_4_address = 4
furnace_5_address = 5
furnace_6_address = 6
furnace_7_address = 7
furnace_8_address = 8

[alarm]
# 报警配置
temperature_threshold = 5.0
communication_timeout = 3
alarm_check_interval = 5.0

[database]
# 数据库配置
db_file = furnace_control.db
backup_interval = 24
max_backup_files = 7

[logging]
# 日志配置
log_level = INFO
log_file = system.log
max_log_size = 10MB
backup_count = 5

[ui]
# 界面配置
window_width = 1200
window_height = 800
refresh_interval = 1000
chart_max_points = 1000
