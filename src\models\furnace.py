#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炉子数据模型
"""

from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


class FurnaceStatus(Enum):
    """炉子状态枚举"""
    IDLE = "空闲"
    WORKING = "工作中"
    FAULT = "故障"
    COMM_ERROR = "通讯中断"


@dataclass
class FurnaceData:
    """炉子实时数据"""
    furnace_id: int
    status: FurnaceStatus
    current_temp: float  # 当前温度(PV)
    set_temp: float      # 设定温度(SV)
    timestamp: datetime
    task_id: Optional[str] = None
    task_start_time: Optional[datetime] = None
    task_end_time: Optional[datetime] = None
    next_task_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class AlarmData:
    """报警数据"""
    alarm_id: Optional[int]
    furnace_id: int
    alarm_type: str
    alarm_message: str
    start_time: datetime
    end_time: Optional[datetime] = None
    is_active: bool = True
    
    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()
