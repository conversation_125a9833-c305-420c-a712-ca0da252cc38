#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QGridLayout, QLabel, QPushButton, QMenuBar, QMenu,
                            QStatusBar, QFrame, QMessageBox, QApplication, QScrollArea)
from PyQt6.QtCore import Qt, QTimer, pyqtSlot
from PyQt6.QtGui import QFont, QAction, QPalette, QColor
from datetime import datetime
from typing import Dict

from ..core.task_manager import TaskManager
from ..models.furnace import FurnaceStatus
from .furnace_widget import FurnaceWidget
from .ui_config import UIConfig, apply_window_geometry
from .toolbar_style import apply_styles


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        self.task_manager = task_manager
        self.furnace_widgets: Dict[int, FurnaceWidget] = {}
        
        # 子窗口
        self.process_manager_window = None
        self.task_scheduler_window = None
        self.history_viewer_window = None
        self.alarm_window = None
        
        self.init_ui()
        self.connect_signals()
        
        # 定时更新界面
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("烧结炉集中控制与数据管理系统")

        # 使用配置文件设置窗口几何
        apply_window_geometry(self, UIConfig.get_main_window_geometry)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("烧结炉集中控制与数据管理系统")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 炉子监控区域 - 使用滚动区域包装
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建一个容器来居中显示炉子组件
        container_widget = QWidget()
        container_layout = QHBoxLayout(container_widget)
        container_layout.setContentsMargins(0, 0, 0, 0)

        self.furnace_frame = QFrame()
        self.furnace_frame.setFrameStyle(QFrame.Shape.Box)
        self.furnace_layout = QGridLayout(self.furnace_frame)

        # 设置网格布局的间距和边距
        self.furnace_layout.setSpacing(10)
        self.furnace_layout.setContentsMargins(15, 15, 15, 15)

        # 创建8个炉子监控组件
        for i in range(1, 9):
            furnace_widget = FurnaceWidget(i, self.task_manager)
            self.furnace_widgets[i] = furnace_widget

        # 将炉子框架添加到容器中，并居中
        container_layout.addStretch(1)  # 左侧弹性空间
        container_layout.addWidget(self.furnace_frame)
        container_layout.addStretch(1)  # 右侧弹性空间

        # 初始布局
        self.arrange_furnace_widgets()

        scroll_area.setWidget(container_widget)
        main_layout.addWidget(scroll_area)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("系统已启动")

        # 应用样式
        apply_styles(self)

    def arrange_furnace_widgets(self):
        """根据窗口大小安排炉子组件布局"""
        # 清除现有布局
        for i in reversed(range(self.furnace_layout.count())):
            self.furnace_layout.itemAt(i).widget().setParent(None)

        # 清除之前的拉伸因子
        for i in range(10):  # 清除可能的列拉伸因子
            self.furnace_layout.setColumnStretch(i, 0)
        for i in range(10):  # 清除可能的行拉伸因子
            self.furnace_layout.setRowStretch(i, 0)

        # 获取当前窗口尺寸
        window_width = self.width()
        window_height = self.height()

        # 计算可用区域（减去标题、状态栏等）
        available_width = window_width - 40  # 减去边距
        available_height = window_height - 150  # 减去标题、菜单栏、状态栏等

        # 炉子组件的最小尺寸
        min_widget_width = 220
        min_widget_height = 160

        # 根据可用空间智能计算最佳列数
        max_cols_by_width = max(1, available_width // (min_widget_width + 10))  # 10是间距

        # 对于8个组件，找到最佳的行列组合
        best_cols = 4
        best_rows = 2

        # 尝试不同的列数，找到最合适的布局
        options = [
            (1, 8),  # 1列8行
            (2, 4),  # 2列4行
            (3, 3),  # 3列3行（最后一行1个）
            (4, 2),  # 4列2行
        ]

        for cols, rows in options:
            if cols <= max_cols_by_width:
                # 检查这种布局是否合适
                required_width = cols * min_widget_width + (cols - 1) * 10
                required_height = rows * min_widget_height + (rows - 1) * 10

                if required_width <= available_width and required_height <= available_height:
                    best_cols = cols
                    best_rows = rows

        # 如果窗口太小，强制使用1列
        if available_width < min_widget_width + 20:
            best_cols = 1
            best_rows = 8

        # 根据布局调整组件尺寸
        self.adjust_widget_sizes(best_cols, available_width, available_height)

        # 重新排列组件
        for i in range(1, 9):
            row = (i - 1) // best_cols
            col = (i - 1) % best_cols

            furnace_widget = self.furnace_widgets[i]
            self.furnace_layout.addWidget(furnace_widget, row, col)

        # 设置列的拉伸因子 - 只对实际使用的列设置
        for col in range(best_cols):
            self.furnace_layout.setColumnStretch(col, 1)

        # 设置行的拉伸因子 - 只对实际使用的行设置
        actual_rows = (8 + best_cols - 1) // best_cols
        for row in range(actual_rows):
            self.furnace_layout.setRowStretch(row, 1)

    def adjust_widget_sizes(self, cols, available_width, available_height):
        """根据布局调整组件尺寸"""
        # 计算每个组件的理想尺寸
        spacing = 10
        margins = 30

        ideal_width = (available_width - margins - (cols - 1) * spacing) // cols

        # 计算行数
        rows = (8 + cols - 1) // cols
        ideal_height = (available_height - margins - (rows - 1) * spacing) // rows

        # 设置合理的尺寸范围，允许在大窗口时放大
        min_width = 220
        min_height = 160

        # 根据窗口大小动态调整最大尺寸限制
        window_width = self.width()
        window_height = self.height()

        if window_width > 1600:  # 大窗口
            max_width = min(500, ideal_width)
            max_height = min(400, ideal_height)
        elif window_width > 1200:  # 中等窗口
            max_width = min(400, ideal_width)
            max_height = min(320, ideal_height)
        else:  # 小窗口
            max_width = min(350, ideal_width)
            max_height = min(280, ideal_height)

        # 确保尺寸不小于最小值
        ideal_width = max(min_width, max_width)
        ideal_height = max(min_height, max_height)

        # 应用到所有炉子组件
        for i in range(1, 9):
            widget = self.furnace_widgets[i]
            widget.setMaximumSize(ideal_width, ideal_height)
            widget.setMinimumSize(min_width, min_height)

            # 如果是单列布局，允许更大的宽度
            if cols == 1:
                single_col_width = min(600, available_width - 40)
                widget.setMaximumSize(single_col_width, ideal_height)

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        if hasattr(self, 'furnace_widgets'):
            self.arrange_furnace_widgets()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        # 新建任务
        new_task_action = QAction('新建任务', self)
        new_task_action.setShortcut('Ctrl+N')
        new_task_action.triggered.connect(self.open_task_scheduler)
        file_menu.addAction(new_task_action)

        file_menu.addSeparator()

        # 导出数据
        export_action = QAction('导出数据', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出系统', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工艺菜单
        process_menu = menubar.addMenu('工艺')

        # 工艺库管理
        process_manager_action = QAction('工艺库管理', self)
        process_manager_action.setShortcut('Ctrl+P')
        process_manager_action.triggered.connect(self.open_process_manager)
        process_menu.addAction(process_manager_action)

        # 新建工艺
        new_process_action = QAction('新建工艺', self)
        new_process_action.triggered.connect(self.create_new_process)
        process_menu.addAction(new_process_action)

        # 导入工艺
        import_process_action = QAction('导入工艺', self)
        import_process_action.triggered.connect(self.import_process)
        process_menu.addAction(import_process_action)

        # 任务菜单
        task_menu = menubar.addMenu('任务')

        # 任务调度
        task_scheduler_action = QAction('任务调度', self)
        task_scheduler_action.setShortcut('Ctrl+T')
        task_scheduler_action.triggered.connect(self.open_task_scheduler)
        task_menu.addAction(task_scheduler_action)

        task_menu.addSeparator()

        # 停止所有任务
        stop_all_action = QAction('停止所有任务', self)
        stop_all_action.triggered.connect(self.stop_all_tasks)
        task_menu.addAction(stop_all_action)

        # 暂停所有任务
        pause_all_action = QAction('暂停所有任务', self)
        pause_all_action.triggered.connect(self.pause_all_tasks)
        task_menu.addAction(pause_all_action)

        # 查询菜单
        query_menu = menubar.addMenu('查询')

        # 历史记录
        history_action = QAction('历史记录', self)
        history_action.setShortcut('Ctrl+H')
        history_action.triggered.connect(self.open_history_viewer)
        query_menu.addAction(history_action)

        # 报警记录
        alarm_action = QAction('报警记录', self)
        alarm_action.setShortcut('Ctrl+A')
        alarm_action.triggered.connect(self.open_alarm_window)
        query_menu.addAction(alarm_action)

        query_menu.addSeparator()

        # 实时数据
        realtime_action = QAction('实时数据监控', self)
        realtime_action.triggered.connect(self.show_realtime_data)
        query_menu.addAction(realtime_action)

        # 设置菜单
        settings_menu = menubar.addMenu('设置')

        # 界面设置
        ui_settings_action = QAction('界面设置', self)
        ui_settings_action.triggered.connect(self.open_ui_settings)
        settings_menu.addAction(ui_settings_action)

        # 通讯设置
        comm_settings_action = QAction('通讯设置', self)
        comm_settings_action.triggered.connect(self.open_comm_settings)
        settings_menu.addAction(comm_settings_action)

        # 报警设置
        alarm_settings_action = QAction('报警设置', self)
        alarm_settings_action.triggered.connect(self.open_alarm_settings)
        settings_menu.addAction(alarm_settings_action)

        settings_menu.addSeparator()

        # 系统配置
        system_config_action = QAction('系统配置', self)
        system_config_action.triggered.connect(self.open_system_config)
        settings_menu.addAction(system_config_action)

        # 工具菜单
        tools_menu = menubar.addMenu('工具')

        # 数据备份
        backup_action = QAction('数据备份', self)
        backup_action.triggered.connect(self.backup_data)
        tools_menu.addAction(backup_action)

        # 数据恢复
        restore_action = QAction('数据恢复', self)
        restore_action.triggered.connect(self.restore_data)
        tools_menu.addAction(restore_action)

        tools_menu.addSeparator()

        # 系统诊断
        diagnostic_action = QAction('系统诊断', self)
        diagnostic_action.triggered.connect(self.run_diagnostic)
        tools_menu.addAction(diagnostic_action)

        # 日志查看
        log_action = QAction('查看日志', self)
        log_action.triggered.connect(self.view_logs)
        tools_menu.addAction(log_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助')

        # 使用手册
        manual_action = QAction('使用手册', self)
        manual_action.setShortcut('F1')
        manual_action.triggered.connect(self.show_manual)
        help_menu.addAction(manual_action)

        # 快捷键
        shortcuts_action = QAction('快捷键说明', self)
        shortcuts_action.triggered.connect(self.show_shortcuts)
        help_menu.addAction(shortcuts_action)

        help_menu.addSeparator()

        # 关于
        about_action = QAction('关于系统', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def connect_signals(self):
        """连接信号"""
        # 连接任务管理器信号
        self.task_manager.task_started.connect(self.on_task_started)
        self.task_manager.task_completed.connect(self.on_task_completed)
        self.task_manager.task_stopped.connect(self.on_task_stopped)
        self.task_manager.task_failed.connect(self.on_task_failed)
        self.task_manager.alarm_triggered.connect(self.on_alarm_triggered)
        self.task_manager.alarm_cleared.connect(self.on_alarm_cleared)
        
        # 连接通讯管理器信号
        self.task_manager.comm_manager.furnace_data_updated.connect(self.on_furnace_data_updated)
    
    @pyqtSlot()
    def update_display(self):
        """更新显示"""
        try:
            # 更新状态栏
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            running_count = len(self.task_manager.get_running_tasks())
            alarm_count = len(self.task_manager.get_active_alarms())
            
            status_text = f"当前时间: {current_time} | 运行任务: {running_count} | 活动报警: {alarm_count}"
            self.status_bar.showMessage(status_text)
            
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    @pyqtSlot(int, object)
    def on_furnace_data_updated(self, furnace_id: int, furnace_data):
        """炉子数据更新"""
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].update_data(furnace_data)
    
    @pyqtSlot(str)
    def on_task_started(self, task_id: str):
        """任务开始"""
        self.status_bar.showMessage(f"任务已开始: {task_id}", 3000)
    
    @pyqtSlot(str)
    def on_task_completed(self, task_id: str):
        """任务完成"""
        self.status_bar.showMessage(f"任务已完成: {task_id}", 3000)
    
    @pyqtSlot(str)
    def on_task_stopped(self, task_id: str):
        """任务停止"""
        self.status_bar.showMessage(f"任务已停止: {task_id}", 3000)
    
    @pyqtSlot(str)
    def on_task_failed(self, task_id: str):
        """任务失败"""
        self.status_bar.showMessage(f"任务失败: {task_id}", 5000)
    
    @pyqtSlot(int, str)
    def on_alarm_triggered(self, furnace_id: int, message: str):
        """报警触发"""
        self.status_bar.showMessage(f"炉子{furnace_id}报警: {message}", 5000)
        
        # 更新炉子组件显示
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].set_alarm_state(True)
    
    @pyqtSlot(int, str)
    def on_alarm_cleared(self, furnace_id: int, message: str):
        """报警清除"""
        self.status_bar.showMessage(f"炉子{furnace_id}: {message}", 3000)
        
        # 更新炉子组件显示
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].set_alarm_state(False)
    
    def open_process_manager(self):
        """打开工艺库管理窗口"""
        from .process_manager_window import ProcessManagerWindow
        if not self.process_manager_window:
            self.process_manager_window = ProcessManagerWindow(self.task_manager)
        self.process_manager_window.show()
        self.process_manager_window.raise_()
    
    def open_task_scheduler(self):
        """打开任务调度窗口"""
        from .task_scheduler_window import TaskSchedulerWindow
        if not self.task_scheduler_window:
            self.task_scheduler_window = TaskSchedulerWindow(self.task_manager)
        self.task_scheduler_window.show()
        self.task_scheduler_window.raise_()

    def open_history_viewer(self):
        """打开历史记录窗口"""
        from .history_viewer_window import HistoryViewerWindow
        if not self.history_viewer_window:
            self.history_viewer_window = HistoryViewerWindow(self.task_manager)
        self.history_viewer_window.show()
        self.history_viewer_window.raise_()

    def open_alarm_window(self):
        """打开报警记录窗口"""
        from .alarm_window import AlarmWindow
        if not self.alarm_window:
            self.alarm_window = AlarmWindow(self.task_manager)
        self.alarm_window.show()
        self.alarm_window.raise_()

    def open_ui_settings(self):
        """打开界面设置对话框"""
        from .ui_settings_dialog import UISettingsDialog
        dialog = UISettingsDialog(self)
        dialog.settings_changed.connect(self.on_ui_settings_changed)
        dialog.exec()

    def on_ui_settings_changed(self):
        """UI设置改变时的处理"""
        # 重新加载UI配置
        UIConfig._user_settings = None  # 清除缓存，强制重新加载

        # 可以在这里添加实时应用设置的代码
        # 比如调整字体大小等
        pass

    # 新增的菜单功能方法
    def export_data(self):
        """导出数据"""
        QMessageBox.information(self, "导出数据", "数据导出功能开发中...")

    def create_new_process(self):
        """新建工艺"""
        self.open_process_manager()

    def import_process(self):
        """导入工艺"""
        QMessageBox.information(self, "导入工艺", "工艺导入功能开发中...")

    def stop_all_tasks(self):
        """停止所有任务"""
        reply = QMessageBox.question(self, "确认", "确定要停止所有正在运行的任务吗？")
        if reply == QMessageBox.StandardButton.Yes:
            # 调用任务管理器停止所有任务
            self.task_manager.stop_all_tasks()
            QMessageBox.information(self, "提示", "已停止所有任务")

    def pause_all_tasks(self):
        """暂停所有任务"""
        reply = QMessageBox.question(self, "确认", "确定要暂停所有正在运行的任务吗？")
        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "暂停任务", "任务暂停功能开发中...")

    def show_realtime_data(self):
        """显示实时数据监控"""
        QMessageBox.information(self, "实时数据", "实时数据监控功能开发中...")

    def open_comm_settings(self):
        """打开通讯设置"""
        QMessageBox.information(self, "通讯设置", "通讯设置功能开发中...")

    def open_alarm_settings(self):
        """打开报警设置"""
        QMessageBox.information(self, "报警设置", "报警设置功能开发中...")

    def open_system_config(self):
        """打开系统配置"""
        QMessageBox.information(self, "系统配置", "系统配置功能开发中...")

    def backup_data(self):
        """数据备份"""
        QMessageBox.information(self, "数据备份", "数据备份功能开发中...")

    def restore_data(self):
        """数据恢复"""
        QMessageBox.information(self, "数据恢复", "数据恢复功能开发中...")

    def run_diagnostic(self):
        """运行系统诊断"""
        QMessageBox.information(self, "系统诊断", "系统诊断功能开发中...")

    def view_logs(self):
        """查看日志"""
        QMessageBox.information(self, "查看日志", "日志查看功能开发中...")

    def show_manual(self):
        """显示使用手册"""
        QMessageBox.information(self, "使用手册", "使用手册功能开发中...")

    def show_shortcuts(self):
        """显示快捷键说明"""
        shortcuts_text = """
快捷键说明：

文件操作：
Ctrl+N - 新建任务
Ctrl+E - 导出数据
Ctrl+Q - 退出系统

功能模块：
Ctrl+P - 工艺库管理
Ctrl+T - 任务调度
Ctrl+H - 历史记录
Ctrl+A - 报警记录

帮助：
F1 - 使用手册
        """
        QMessageBox.information(self, "快捷键说明", shortcuts_text.strip())
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "烧结炉集中控制与数据管理系统 v1.0\n\n"
                         "基于Python和PyQt6开发\n"
                         "支持8台烧结炉的集中监控和控制")
    
    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(self, '确认退出', 
                                   '确定要退出系统吗？',
                                   QMessageBox.StandardButton.Yes | 
                                   QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # 清理资源
            self.update_timer.stop()
            event.accept()
        else:
            event.ignore()
