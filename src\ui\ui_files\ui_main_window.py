# Form implementation generated from reading ui file 'D:\Code\kongzhi\src\ui\ui_files\main_window.ui'
#
# Created by: PyQt6 UI code generator 6.9.1
#
# WARNING: Any manual changes made to this file will be lost when pyuic6 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt6 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1400, 900)
        MainWindow.setMinimumSize(QtCore.QSize(800, 600))
        self.centralwidget = QtWidgets.QWidget(parent=MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.main_layout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.main_layout.setObjectName("main_layout")
        self.scroll_area = QtWidgets.QScrollArea(parent=self.centralwidget)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setObjectName("scroll_area")
        self.scroll_area_contents = QtWidgets.QWidget()
        self.scroll_area_contents.setGeometry(QtCore.QRect(0, 0, 1398, 847))
        self.scroll_area_contents.setObjectName("scroll_area_contents")
        self.container_layout = QtWidgets.QHBoxLayout(self.scroll_area_contents)
        self.container_layout.setContentsMargins(0, 0, 0, 0)
        self.container_layout.setSpacing(0)
        self.container_layout.setObjectName("container_layout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Policy.Expanding, QtWidgets.QSizePolicy.Policy.Minimum)
        self.container_layout.addItem(spacerItem)
        self.furnace_frame = QtWidgets.QFrame(parent=self.scroll_area_contents)
        self.furnace_frame.setFrameShape(QtWidgets.QFrame.Shape.Box)
        self.furnace_frame.setFrameShadow(QtWidgets.QFrame.Shadow.Raised)
        self.furnace_frame.setObjectName("furnace_frame")
        self.furnace_layout = QtWidgets.QGridLayout(self.furnace_frame)
        self.furnace_layout.setContentsMargins(15, 15, 15, 15)
        self.furnace_layout.setSpacing(10)
        self.furnace_layout.setObjectName("furnace_layout")
        self.container_layout.addWidget(self.furnace_frame)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Policy.Expanding, QtWidgets.QSizePolicy.Policy.Minimum)
        self.container_layout.addItem(spacerItem1)
        self.scroll_area.setWidget(self.scroll_area_contents)
        self.main_layout.addWidget(self.scroll_area)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(parent=MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1400, 22))
        self.menubar.setObjectName("menubar")
        self.menu_file = QtWidgets.QMenu(parent=self.menubar)
        self.menu_file.setObjectName("menu_file")
        self.menu_process = QtWidgets.QMenu(parent=self.menubar)
        self.menu_process.setObjectName("menu_process")
        self.menu_task = QtWidgets.QMenu(parent=self.menubar)
        self.menu_task.setObjectName("menu_task")
        self.menu_query = QtWidgets.QMenu(parent=self.menubar)
        self.menu_query.setObjectName("menu_query")
        self.menu_tools = QtWidgets.QMenu(parent=self.menubar)
        self.menu_tools.setObjectName("menu_tools")
        self.menu_help = QtWidgets.QMenu(parent=self.menubar)
        self.menu_help.setObjectName("menu_help")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(parent=MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.menubar.addAction(self.menu_file.menuAction())
        self.menubar.addAction(self.menu_process.menuAction())
        self.menubar.addAction(self.menu_task.menuAction())
        self.menubar.addAction(self.menu_query.menuAction())
        self.menubar.addAction(self.menu_tools.menuAction())
        self.menubar.addAction(self.menu_help.menuAction())

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "烧结炉集中控制与数据管理系统"))
        self.menu_file.setTitle(_translate("MainWindow", "文件"))
        self.menu_process.setTitle(_translate("MainWindow", "工艺"))
        self.menu_task.setTitle(_translate("MainWindow", "任务"))
        self.menu_query.setTitle(_translate("MainWindow", "查询"))
        self.menu_tools.setTitle(_translate("MainWindow", "工具"))
        self.menu_help.setTitle(_translate("MainWindow", "帮助"))
