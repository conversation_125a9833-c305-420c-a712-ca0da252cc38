#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工艺程序数据模型
"""

from dataclasses import dataclass, field
from typing import List, Optional
from datetime import datetime


@dataclass
class ProcessStep:
    """工艺步骤"""
    step_id: int
    target_temp: float      # 目标温度 (°C)
    rate: float            # 升温/保温速率 (°C/分钟)
    duration: int          # 持续时间 (分钟) - 由速率自动计算
    
    def calculate_duration(self, current_temp: float) -> int:
        """根据当前温度和目标温度计算持续时间"""
        if self.rate <= 0:
            return 0
        temp_diff = abs(self.target_temp - current_temp)
        return int(temp_diff / self.rate)


@dataclass
class Process:
    """工艺程序"""
    process_id: Optional[str]
    name: str
    description: str
    steps: List[ProcessStep] = field(default_factory=list)
    created_time: datetime = field(default_factory=datetime.now)
    updated_time: datetime = field(default_factory=datetime.now)
    
    def add_step(self, target_temp: float, rate: float) -> ProcessStep:
        """添加工艺步骤"""
        step_id = len(self.steps) + 1
        step = ProcessStep(step_id, target_temp, rate, 0)
        self.steps.append(step)
        self.updated_time = datetime.now()
        return step
    
    def remove_step(self, step_id: int) -> bool:
        """删除工艺步骤"""
        for i, step in enumerate(self.steps):
            if step.step_id == step_id:
                self.steps.pop(i)
                # 重新编号
                for j, remaining_step in enumerate(self.steps[i:], i):
                    remaining_step.step_id = j + 1
                self.updated_time = datetime.now()
                return True
        return False
    
    def get_total_duration(self, start_temp: float = 20.0) -> int:
        """计算总工艺时间"""
        total_duration = 0
        current_temp = start_temp
        
        for step in self.steps:
            duration = step.calculate_duration(current_temp)
            total_duration += duration
            current_temp = step.target_temp
            
        return total_duration
