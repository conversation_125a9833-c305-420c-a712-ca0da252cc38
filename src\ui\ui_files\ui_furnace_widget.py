# Form implementation generated from reading ui file 'D:\Code\kongzhi\src\ui\ui_files\furnace_widget.ui'
#
# Created by: PyQt6 UI code generator 6.9.1
#
# WARNING: Any manual changes made to this file will be lost when pyuic6 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt6 import QtCore, QtGui, QtWidgets


class Ui_FurnaceWidget(object):
    def setupUi(self, FurnaceWidget):
        FurnaceWidget.setObjectName("FurnaceWidget")
        FurnaceWidget.resize(280, 200)
        FurnaceWidget.setMinimumSize(QtCore.QSize(220, 160))
        FurnaceWidget.setFrameShape(QtWidgets.QFrame.Shape.Box)
        FurnaceWidget.setFrameShadow(QtWidgets.QFrame.Shadow.Raised)
        self.main_layout = QtWidgets.QVBoxLayout(FurnaceWidget)
        self.main_layout.setContentsMargins(8, 6, 8, 6)
        self.main_layout.setSpacing(4)
        self.main_layout.setObjectName("main_layout")
        self.title_label = QtWidgets.QLabel(parent=FurnaceWidget)
        self.title_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        self.title_label.setFont(font)
        self.title_label.setObjectName("title_label")
        self.main_layout.addWidget(self.title_label)
        self.status_label = QtWidgets.QLabel(parent=FurnaceWidget)
        self.status_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.status_label.setFont(font)
        self.status_label.setObjectName("status_label")
        self.main_layout.addWidget(self.status_label)
        self.temp_layout = QtWidgets.QHBoxLayout()
        self.temp_layout.setSpacing(5)
        self.temp_layout.setObjectName("temp_layout")
        self.pv_layout = QtWidgets.QVBoxLayout()
        self.pv_layout.setSpacing(2)
        self.pv_layout.setObjectName("pv_layout")
        self.pv_title = QtWidgets.QLabel(parent=FurnaceWidget)
        self.pv_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        font = QtGui.QFont()
        font.setPointSize(8)
        self.pv_title.setFont(font)
        self.pv_title.setObjectName("pv_title")
        self.pv_layout.addWidget(self.pv_title)
        self.pv_label = QtWidgets.QLabel(parent=FurnaceWidget)
        self.pv_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.pv_label.setFont(font)
        self.pv_label.setObjectName("pv_label")
        self.pv_layout.addWidget(self.pv_label)
        self.temp_layout.addLayout(self.pv_layout)
        self.sv_layout = QtWidgets.QVBoxLayout()
        self.sv_layout.setSpacing(2)
        self.sv_layout.setObjectName("sv_layout")
        self.sv_title = QtWidgets.QLabel(parent=FurnaceWidget)
        self.sv_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        font = QtGui.QFont()
        font.setPointSize(8)
        self.sv_title.setFont(font)
        self.sv_title.setObjectName("sv_title")
        self.sv_layout.addWidget(self.sv_title)
        self.sv_label = QtWidgets.QLabel(parent=FurnaceWidget)
        self.sv_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.sv_label.setFont(font)
        self.sv_label.setObjectName("sv_label")
        self.sv_layout.addWidget(self.sv_label)
        self.temp_layout.addLayout(self.sv_layout)
        self.main_layout.addLayout(self.temp_layout)
        self.time_info_label = QtWidgets.QLabel(parent=FurnaceWidget)
        self.time_info_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.time_info_label.setWordWrap(True)
        font = QtGui.QFont()
        font.setPointSize(8)
        self.time_info_label.setFont(font)
        self.time_info_label.setObjectName("time_info_label")
        self.main_layout.addWidget(self.time_info_label)
        self.button_layout = QtWidgets.QHBoxLayout()
        self.button_layout.setSpacing(3)
        self.button_layout.setObjectName("button_layout")
        self.detail_btn = QtWidgets.QPushButton(parent=FurnaceWidget)
        self.detail_btn.setMaximumSize(QtCore.QSize(16777215, 25))
        self.detail_btn.setObjectName("detail_btn")
        self.button_layout.addWidget(self.detail_btn)
        self.stop_btn = QtWidgets.QPushButton(parent=FurnaceWidget)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setMaximumSize(QtCore.QSize(16777215, 25))
        self.stop_btn.setObjectName("stop_btn")
        self.button_layout.addWidget(self.stop_btn)
        self.main_layout.addLayout(self.button_layout)

        self.retranslateUi(FurnaceWidget)
        QtCore.QMetaObject.connectSlotsByName(FurnaceWidget)

    def retranslateUi(self, FurnaceWidget):
        _translate = QtCore.QCoreApplication.translate
        self.title_label.setText(_translate("FurnaceWidget", "1号炉"))
        self.status_label.setText(_translate("FurnaceWidget", "状态: 通讯中断"))
        self.pv_title.setText(_translate("FurnaceWidget", "当前温度(PV)"))
        self.pv_label.setText(_translate("FurnaceWidget", "0.0°C"))
        self.sv_title.setText(_translate("FurnaceWidget", "设定温度(SV)"))
        self.sv_label.setText(_translate("FurnaceWidget", "0.0°C"))
        self.time_info_label.setText(_translate("FurnaceWidget", "空闲中"))
        self.detail_btn.setText(_translate("FurnaceWidget", "详情"))
        self.stop_btn.setText(_translate("FurnaceWidget", "停止"))
