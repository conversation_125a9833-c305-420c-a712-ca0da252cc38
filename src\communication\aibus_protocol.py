#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIBUS协议实现
"""

import struct
from typing import Tu<PERSON>, Optional, Union
from enum import Enum


class AIBUSCommand(Enum):
    """AIBUS命令枚举"""
    READ_PV = 0x01      # 读取测量值(PV)
    READ_SV = 0x02      # 读取设定值(SV)
    WRITE_SV = 0x06     # 写入设定值(SV)
    READ_STATUS = 0x03  # 读取状态
    READ_OUTPUT = 0x04  # 读取输出值
    WRITE_OUTPUT = 0x10 # 写入输出值


class AIBUSProtocol:
    """AIBUS协议处理类"""
    
    def __init__(self):
        self.STX = 0x02  # 起始字符
        self.ETX = 0x03  # 结束字符
    
    def calculate_checksum(self, data: bytes) -> int:
        """计算校验和"""
        return sum(data) & 0xFF
    
    def build_command(self, device_addr: int, command: AIBUSCommand, 
                     data: Optional[bytes] = None) -> bytes:
        """构建AIBUS命令帧"""
        if not (1 <= device_addr <= 247):
            raise ValueError("设备地址必须在1-247之间")
        
        # 构建命令帧
        frame = bytearray()
        frame.append(self.STX)
        frame.append(device_addr)
        frame.append(command.value)
        
        if data:
            frame.extend(data)
        
        # 计算校验和
        checksum = self.calculate_checksum(frame[1:])  # 不包括STX
        frame.append(checksum)
        frame.append(self.ETX)
        
        return bytes(frame)
    
    def parse_response(self, response: bytes) -> Tuple[bool, Optional[dict]]:
        """解析AIBUS响应帧"""
        if len(response) < 5:  # 最小帧长度
            return False, None
        
        if response[0] != self.STX or response[-1] != self.ETX:
            return False, None
        
        # 验证校验和
        data_part = response[1:-2]  # 不包括STX和ETX和校验和
        received_checksum = response[-2]
        calculated_checksum = self.calculate_checksum(data_part)
        
        if received_checksum != calculated_checksum:
            return False, None
        
        # 解析数据
        device_addr = response[1]
        command = response[2]
        data = response[3:-2] if len(response) > 5 else b''
        
        result = {
            'device_addr': device_addr,
            'command': command,
            'data': data
        }
        
        return True, result
    
    def build_read_pv_command(self, device_addr: int) -> bytes:
        """构建读取PV命令"""
        return self.build_command(device_addr, AIBUSCommand.READ_PV)
    
    def build_read_sv_command(self, device_addr: int) -> bytes:
        """构建读取SV命令"""
        return self.build_command(device_addr, AIBUSCommand.READ_SV)
    
    def build_write_sv_command(self, device_addr: int, value: float) -> bytes:
        """构建写入SV命令"""
        # 将浮点数转换为整数（假设精度为0.1度）
        int_value = int(value * 10)
        data = struct.pack('>H', int_value)  # 大端序，16位无符号整数
        return self.build_command(device_addr, AIBUSCommand.WRITE_SV, data)
    
    def build_read_status_command(self, device_addr: int) -> bytes:
        """构建读取状态命令"""
        return self.build_command(device_addr, AIBUSCommand.READ_STATUS)
    
    def parse_temperature_response(self, response_data: bytes) -> Optional[float]:
        """解析温度响应数据"""
        if len(response_data) < 2:
            return None
        
        try:
            # 假设温度数据为16位整数，精度0.1度
            int_value = struct.unpack('>H', response_data[:2])[0]
            return int_value / 10.0
        except struct.error:
            return None
    
    def parse_status_response(self, response_data: bytes) -> Optional[dict]:
        """解析状态响应数据"""
        if len(response_data) < 1:
            return None
        
        status_byte = response_data[0]
        
        # 解析状态位（根据实际仪表文档调整）
        status = {
            'alarm': bool(status_byte & 0x01),      # 报警
            'output1': bool(status_byte & 0x02),    # 输出1
            'output2': bool(status_byte & 0x04),    # 输出2
            'sensor_error': bool(status_byte & 0x08), # 传感器错误
            'heating': bool(status_byte & 0x10),    # 加热中
            'cooling': bool(status_byte & 0x20),    # 冷却中
        }
        
        return status
