@echo off
echo 烧结炉集中控制与数据管理系统
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import PyQt6" >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: PyQt6未安装，正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动系统...
python main.py

if %errorlevel% neq 0 (
    echo.
    echo 系统启动失败，请检查错误信息
    pause
)
