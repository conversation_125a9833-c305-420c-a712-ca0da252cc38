#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务详情窗口
"""

import pyqtgraph as pg
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QSplitter, QWidget,
                            QGroupBox, QGridLayout, QMessageBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSlot
from PyQt6.QtGui import QFont
from datetime import datetime, timedelta
import numpy as np

from ..core.task_manager import TaskManager
from ..models.task import TaskStatus
from .ui_config import UIConfig, apply_window_geometry


class TaskDetailWindow(QDialog):
    """任务详情窗口"""
    
    def __init__(self, furnace_id: int, task_manager: TaskManager):
        super().__init__()
        
        self.furnace_id = furnace_id
        self.task_manager = task_manager
        self.current_task = None
        
        self.init_ui()
        self.update_data()
        
        # 定时更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.start(2000)  # 每2秒更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"{self.furnace_id}号炉 - 任务详情")

        # 使用配置文件设置窗口几何
        apply_window_geometry(self, UIConfig.get_task_detail_geometry)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel(f"{self.furnace_id}号炉任务详情")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧信息面板
        info_widget = self.create_info_panel()
        splitter.addWidget(info_widget)
        
        # 右侧图表面板
        chart_widget = self.create_chart_panel()
        splitter.addWidget(chart_widget)
        
        # 设置分割比例
        splitter.setSizes([300, 700])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.export_btn = QPushButton("导出报告")
        self.export_btn.clicked.connect(self.export_report)
        button_layout.addWidget(self.export_btn)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.update_data)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_info_panel(self):
        """创建信息面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout(basic_group)
        
        basic_layout.addWidget(QLabel("任务ID:"), 0, 0)
        self.task_id_label = QLabel("无")
        basic_layout.addWidget(self.task_id_label, 0, 1)
        
        basic_layout.addWidget(QLabel("工艺名称:"), 1, 0)
        self.process_name_label = QLabel("无")
        basic_layout.addWidget(self.process_name_label, 1, 1)
        
        basic_layout.addWidget(QLabel("任务状态:"), 2, 0)
        self.task_status_label = QLabel("无")
        basic_layout.addWidget(self.task_status_label, 2, 1)
        
        basic_layout.addWidget(QLabel("计划开始:"), 3, 0)
        self.scheduled_start_label = QLabel("无")
        basic_layout.addWidget(self.scheduled_start_label, 3, 1)
        
        basic_layout.addWidget(QLabel("实际开始:"), 4, 0)
        self.actual_start_label = QLabel("无")
        basic_layout.addWidget(self.actual_start_label, 4, 1)
        
        basic_layout.addWidget(QLabel("实际结束:"), 5, 0)
        self.actual_end_label = QLabel("无")
        basic_layout.addWidget(self.actual_end_label, 5, 1)
        
        layout.addWidget(basic_group)
        
        # 当前状态组
        status_group = QGroupBox("当前状态")
        status_layout = QGridLayout(status_group)
        
        status_layout.addWidget(QLabel("当前温度:"), 0, 0)
        self.current_temp_label = QLabel("0.0°C")
        status_layout.addWidget(self.current_temp_label, 0, 1)
        
        status_layout.addWidget(QLabel("设定温度:"), 1, 0)
        self.set_temp_label = QLabel("0.0°C")
        status_layout.addWidget(self.set_temp_label, 1, 1)
        
        status_layout.addWidget(QLabel("当前步骤:"), 2, 0)
        self.current_step_label = QLabel("0/0")
        status_layout.addWidget(self.current_step_label, 2, 1)
        
        status_layout.addWidget(QLabel("运行时间:"), 3, 0)
        self.runtime_label = QLabel("00:00:00")
        status_layout.addWidget(self.runtime_label, 3, 1)
        
        layout.addWidget(status_group)
        
        # 工艺步骤组
        steps_group = QGroupBox("工艺步骤")
        steps_layout = QVBoxLayout(steps_group)
        
        self.steps_text = QTextEdit()
        self.steps_text.setMaximumHeight(150)
        self.steps_text.setReadOnly(True)
        steps_layout.addWidget(self.steps_text)
        
        layout.addWidget(steps_group)
        
        layout.addStretch()
        
        return widget
    
    def create_chart_panel(self):
        """创建图表面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 温度曲线图
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', '温度', units='°C')
        self.plot_widget.setLabel('bottom', '时间', units='分钟')
        self.plot_widget.setTitle('温度曲线')
        self.plot_widget.addLegend()
        
        # 设置网格
        self.plot_widget.showGrid(x=True, y=True)
        
        # 创建曲线
        self.pv_curve = self.plot_widget.plot(pen='r', name='实际温度(PV)')
        self.sv_curve = self.plot_widget.plot(pen='b', name='设定温度(SV)')
        
        layout.addWidget(self.plot_widget)
        
        return widget
    
    def update_data(self):
        """更新数据"""
        try:
            # 获取当前任务
            running_tasks = self.task_manager.get_running_tasks()
            self.current_task = running_tasks.get(self.furnace_id)
            
            if self.current_task:
                self.update_task_info()
                self.update_chart()
            else:
                self.clear_task_info()
                self.clear_chart()
            
            # 更新炉子状态
            self.update_furnace_status()
            
        except Exception as e:
            print(f"更新任务详情失败: {e}")
    
    def update_task_info(self):
        """更新任务信息"""
        try:
            task = self.current_task
            
            # 基本信息
            self.task_id_label.setText(task.task_id or "无")
            self.task_status_label.setText(task.status.value)
            
            if task.scheduled_start_time:
                self.scheduled_start_label.setText(
                    task.scheduled_start_time.strftime("%Y-%m-%d %H:%M:%S")
                )
            
            if task.actual_start_time:
                self.actual_start_label.setText(
                    task.actual_start_time.strftime("%Y-%m-%d %H:%M:%S")
                )
                
                # 计算运行时间
                if task.status == TaskStatus.RUNNING:
                    runtime = datetime.now() - task.actual_start_time
                elif task.actual_end_time:
                    runtime = task.actual_end_time - task.actual_start_time
                else:
                    runtime = timedelta(0)
                
                hours, remainder = divmod(int(runtime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.runtime_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            if task.actual_end_time:
                self.actual_end_label.setText(
                    task.actual_end_time.strftime("%Y-%m-%d %H:%M:%S")
                )
            
            # 获取工艺信息
            process = self.task_manager.db_manager.get_process(task.process_id)
            if process:
                self.process_name_label.setText(process.name)
                
                # 显示工艺步骤
                steps_text = ""
                for i, step in enumerate(process.steps):
                    marker = "→" if i == task.current_step else "  "
                    steps_text += f"{marker} 步骤{i+1}: {step.target_temp}°C, {step.rate}°C/min\n"
                
                self.steps_text.setText(steps_text)
                
                # 更新当前步骤显示
                total_steps = len(process.steps)
                current_step_num = task.current_step + 1 if task.current_step < total_steps else total_steps
                self.current_step_label.setText(f"{current_step_num}/{total_steps}")
            
        except Exception as e:
            print(f"更新任务信息失败: {e}")
    
    def update_furnace_status(self):
        """更新炉子状态"""
        try:
            furnace_data = self.task_manager.comm_manager.get_furnace_data(self.furnace_id)
            if furnace_data:
                self.current_temp_label.setText(f"{furnace_data.current_temp:.1f}°C")
                self.set_temp_label.setText(f"{furnace_data.set_temp:.1f}°C")
            
        except Exception as e:
            print(f"更新炉子状态失败: {e}")
    
    def update_chart(self):
        """更新图表"""
        try:
            if not self.current_task or not self.current_task.temperature_records:
                return
            
            records = self.current_task.temperature_records
            if len(records) < 2:
                return
            
            # 计算时间轴（相对于任务开始时间的分钟数）
            start_time = records[0].timestamp
            times = [(record.timestamp - start_time).total_seconds() / 60 for record in records]
            
            # 温度数据
            pv_temps = [record.actual_temp for record in records]
            sv_temps = [record.set_temp for record in records]
            
            # 更新曲线
            self.pv_curve.setData(times, pv_temps)
            self.sv_curve.setData(times, sv_temps)
            
        except Exception as e:
            print(f"更新图表失败: {e}")
    
    def clear_task_info(self):
        """清空任务信息"""
        self.task_id_label.setText("无任务")
        self.process_name_label.setText("无")
        self.task_status_label.setText("空闲")
        self.scheduled_start_label.setText("无")
        self.actual_start_label.setText("无")
        self.actual_end_label.setText("无")
        self.current_step_label.setText("0/0")
        self.runtime_label.setText("00:00:00")
        self.steps_text.setText("无工艺步骤")
    
    def clear_chart(self):
        """清空图表"""
        self.pv_curve.setData([], [])
        self.sv_curve.setData([], [])
    
    @pyqtSlot()
    def export_report(self):
        """导出报告"""
        try:
            if not self.current_task:
                QMessageBox.information(self, "提示", "当前无任务，无法导出报告")
                return
            
            # 这里可以实现PDF报告生成
            QMessageBox.information(self, "提示", "报告导出功能待实现")
            
        except Exception as e:
            print(f"导出报告失败: {e}")
            QMessageBox.warning(self, "错误", f"导出报告失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.update_timer.stop()
        event.accept()
