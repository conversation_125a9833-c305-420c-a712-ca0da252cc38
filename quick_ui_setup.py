#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速UI设置脚本 - 帮助用户快速配置界面
"""

import json
import os
from PyQt6.QtWidgets import QApplication, QMessageBox


def get_screen_info():
    """获取屏幕信息"""
    app = QApplication([])
    screen = app.primaryScreen()
    if screen:
        geometry = screen.availableGeometry()
        return geometry.width(), geometry.height()
    return 1920, 1080


def create_preset_configs():
    """创建预设配置"""
    screen_width, screen_height = get_screen_info()
    
    presets = {
        "小屏幕 (1366x768)": {
            "main_window": {"width": 1200, "height": 700},
            "process_manager": {"width": 1000, "height": 650},
            "task_scheduler": {"width": 1100, "height": 700},
            "task_detail": {"width": 900, "height": 600},
            "history_viewer": {"width": 1100, "height": 650},
            "alarm_window": {"width": 800, "height": 500},
            "center_windows": True,
            "remember_position": False,
            "auto_resize": True,
            "font_size": "9"
        },
        "中等屏幕 (1920x1080)": {
            "main_window": {"width": 1400, "height": 900},
            "process_manager": {"width": 1200, "height": 800},
            "task_scheduler": {"width": 1300, "height": 850},
            "task_detail": {"width": 1100, "height": 750},
            "history_viewer": {"width": 1350, "height": 800},
            "alarm_window": {"width": 1000, "height": 650},
            "center_windows": True,
            "remember_position": False,
            "auto_resize": True,
            "font_size": "10"
        },
        "大屏幕 (2560x1440)": {
            "main_window": {"width": 1800, "height": 1200},
            "process_manager": {"width": 1500, "height": 1000},
            "task_scheduler": {"width": 1600, "height": 1100},
            "task_detail": {"width": 1400, "height": 950},
            "history_viewer": {"width": 1700, "height": 1000},
            "alarm_window": {"width": 1300, "height": 800},
            "center_windows": True,
            "remember_position": False,
            "auto_resize": True,
            "font_size": "11"
        },
        "超大屏幕 (4K)": {
            "main_window": {"width": 2200, "height": 1400},
            "process_manager": {"width": 1800, "height": 1200},
            "task_scheduler": {"width": 2000, "height": 1300},
            "task_detail": {"width": 1700, "height": 1100},
            "history_viewer": {"width": 2100, "height": 1200},
            "alarm_window": {"width": 1500, "height": 1000},
            "center_windows": True,
            "remember_position": False,
            "auto_resize": True,
            "font_size": "12"
        }
    }
    
    return presets


def recommend_preset():
    """推荐合适的预设"""
    screen_width, screen_height = get_screen_info()
    
    if screen_width <= 1366:
        return "小屏幕 (1366x768)"
    elif screen_width <= 1920:
        return "中等屏幕 (1920x1080)"
    elif screen_width <= 2560:
        return "大屏幕 (2560x1440)"
    else:
        return "超大屏幕 (4K)"


def apply_preset(preset_name):
    """应用预设配置"""
    presets = create_preset_configs()
    
    if preset_name not in presets:
        print(f"错误: 未找到预设 '{preset_name}'")
        return False
    
    settings = presets[preset_name]
    settings_file = "ui_settings.json"
    
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 已应用预设: {preset_name}")
        print(f"✓ 设置已保存到: {settings_file}")
        return True
        
    except Exception as e:
        print(f"✗ 保存设置失败: {e}")
        return False


def show_current_settings():
    """显示当前设置"""
    settings_file = "ui_settings.json"
    
    if not os.path.exists(settings_file):
        print("当前使用默认设置")
        return
    
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        print("当前设置:")
        print(f"  主窗口: {settings['main_window']['width']}x{settings['main_window']['height']}")
        print(f"  工艺管理: {settings['process_manager']['width']}x{settings['process_manager']['height']}")
        print(f"  任务调度: {settings['task_scheduler']['width']}x{settings['task_scheduler']['height']}")
        print(f"  任务详情: {settings['task_detail']['width']}x{settings['task_detail']['height']}")
        print(f"  历史查询: {settings['history_viewer']['width']}x{settings['history_viewer']['height']}")
        print(f"  报警窗口: {settings['alarm_window']['width']}x{settings['alarm_window']['height']}")
        print(f"  字体大小: {settings['font_size']}")
        print(f"  窗口居中: {'是' if settings['center_windows'] else '否'}")
        
    except Exception as e:
        print(f"读取设置失败: {e}")


def interactive_setup():
    """交互式设置"""
    print("烧结炉控制系统 - 快速UI设置")
    print("=" * 50)
    
    # 显示屏幕信息
    screen_width, screen_height = get_screen_info()
    print(f"检测到屏幕分辨率: {screen_width}x{screen_height}")
    
    # 推荐预设
    recommended = recommend_preset()
    print(f"推荐预设: {recommended}")
    print()
    
    # 显示当前设置
    print("当前设置:")
    show_current_settings()
    print()
    
    # 显示可用预设
    presets = create_preset_configs()
    print("可用预设:")
    for i, preset_name in enumerate(presets.keys(), 1):
        print(f"  {i}. {preset_name}")
    print("  0. 退出")
    print()
    
    # 用户选择
    while True:
        try:
            choice = input("请选择预设 (输入数字): ").strip()
            
            if choice == "0":
                print("退出设置")
                break
            
            choice_num = int(choice)
            preset_names = list(presets.keys())
            
            if 1 <= choice_num <= len(preset_names):
                selected_preset = preset_names[choice_num - 1]
                
                print(f"\n选择的预设: {selected_preset}")
                
                # 显示预设详情
                preset_config = presets[selected_preset]
                print("预设详情:")
                print(f"  主窗口: {preset_config['main_window']['width']}x{preset_config['main_window']['height']}")
                print(f"  字体大小: {preset_config['font_size']}")
                
                confirm = input("\n确认应用此预设? (y/n): ").strip().lower()
                
                if confirm in ['y', 'yes', '是']:
                    if apply_preset(selected_preset):
                        print("\n✓ 预设应用成功!")
                        print("重启程序后生效。")
                    else:
                        print("\n✗ 预设应用失败!")
                    break
                else:
                    print("取消应用")
                    continue
            else:
                print("无效选择，请重新输入")
                
        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n\n用户取消")
            break


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1]
        
        if command == "list":
            presets = create_preset_configs()
            print("可用预设:")
            for preset_name in presets.keys():
                print(f"  - {preset_name}")
                
        elif command == "recommend":
            recommended = recommend_preset()
            print(f"推荐预设: {recommended}")
            
        elif command == "current":
            show_current_settings()
            
        elif command == "apply" and len(sys.argv) > 2:
            preset_name = sys.argv[2]
            apply_preset(preset_name)
            
        elif command == "reset":
            settings_file = "ui_settings.json"
            if os.path.exists(settings_file):
                os.remove(settings_file)
                print("✓ 已重置为默认设置")
            else:
                print("当前已是默认设置")
                
        else:
            print("用法:")
            print("  python quick_ui_setup.py list        - 列出所有预设")
            print("  python quick_ui_setup.py recommend   - 显示推荐预设")
            print("  python quick_ui_setup.py current     - 显示当前设置")
            print("  python quick_ui_setup.py apply <预设名> - 应用指定预设")
            print("  python quick_ui_setup.py reset       - 重置为默认设置")
            print("  python quick_ui_setup.py             - 交互式设置")
    else:
        # 交互式模式
        interactive_setup()


if __name__ == "__main__":
    main()
