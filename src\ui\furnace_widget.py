#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炉子监控组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QFont, QPalette, QColor
from datetime import datetime

from ..models.furnace import FurnaceStatus, FurnaceData
from ..core.task_manager import TaskManager


class FurnaceWidget(QFrame):
    """炉子监控组件"""
    
    def __init__(self, furnace_id: int, task_manager: TaskManager):
        super().__init__()
        
        self.furnace_id = furnace_id
        self.task_manager = task_manager
        self.task_detail_window = None
        self.has_alarm = False
        
        self.init_ui()
        self.update_display()
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置合理的尺寸范围，支持大窗口时的放大
        self.setMinimumSize(220, 160)
        # 移除固定的最大尺寸限制，改为在主窗口中动态设置

        # 设置尺寸策略，允许组件根据可用空间调整
        from PyQt6.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)

        self.setFrameStyle(QFrame.Shape.Box)

        layout = QVBoxLayout(self)
        layout.setSpacing(4)
        layout.setContentsMargins(8, 6, 8, 6)
        
        # 炉子编号标题
        self.title_label = QLabel(f"{self.furnace_id}号炉")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_font = QFont()
        self.title_font.setPointSize(12)
        self.title_font.setBold(True)
        self.title_label.setFont(self.title_font)
        layout.addWidget(self.title_label)

        # 状态显示
        self.status_label = QLabel("状态: 通讯中断")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)
        self.status_font = QFont()
        self.status_font.setPointSize(10)
        self.status_font.setBold(True)
        self.status_label.setFont(self.status_font)
        layout.addWidget(self.status_label)
        
        # 温度信息
        temp_layout = QHBoxLayout()
        temp_layout.setSpacing(5)

        # 当前温度
        pv_layout = QVBoxLayout()
        pv_layout.setSpacing(2)
        self.pv_title = QLabel("当前温度(PV)")
        self.pv_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.pv_title_font = QFont()
        self.pv_title_font.setPointSize(8)
        self.pv_title.setFont(self.pv_title_font)
        pv_layout.addWidget(self.pv_title)

        self.pv_label = QLabel("0.0°C")
        self.pv_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.pv_font = QFont()
        self.pv_font.setPointSize(10)
        self.pv_font.setBold(True)
        self.pv_label.setFont(self.pv_font)
        pv_layout.addWidget(self.pv_label)
        temp_layout.addLayout(pv_layout)

        # 设定温度
        sv_layout = QVBoxLayout()
        sv_layout.setSpacing(2)
        self.sv_title = QLabel("设定温度(SV)")
        self.sv_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.sv_title_font = QFont()
        self.sv_title_font.setPointSize(8)
        self.sv_title.setFont(self.sv_title_font)
        sv_layout.addWidget(self.sv_title)

        self.sv_label = QLabel("0.0°C")
        self.sv_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.sv_font = QFont()
        self.sv_font.setPointSize(10)
        self.sv_font.setBold(True)
        self.sv_label.setFont(self.sv_font)
        sv_layout.addWidget(self.sv_label)
        temp_layout.addLayout(sv_layout)

        layout.addLayout(temp_layout)
        
        # 任务时间信息
        self.time_info_label = QLabel("空闲中")
        self.time_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_info_label.setWordWrap(True)
        self.time_font = QFont()
        self.time_font.setPointSize(8)
        self.time_info_label.setFont(self.time_font)
        layout.addWidget(self.time_info_label)

        # 控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(3)

        self.detail_btn = QPushButton("详情")
        self.detail_btn.clicked.connect(self.show_detail)
        self.detail_btn.setMaximumHeight(25)  # 限制按钮高度
        button_layout.addWidget(self.detail_btn)

        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_task)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setMaximumHeight(25)  # 限制按钮高度
        button_layout.addWidget(self.stop_btn)

        layout.addLayout(button_layout)
        
        # 设置默认样式
        self.set_status_style(FurnaceStatus.COMM_ERROR)

    def adjust_font_sizes(self):
        """根据组件大小调整字体"""
        widget_width = self.width()
        widget_height = self.height()

        # 根据组件大小计算合适的字体大小
        if widget_width > 400:  # 大组件
            title_size = 16
            status_size = 12
            temp_size = 12
            temp_title_size = 10
            time_size = 10
        elif widget_width > 300:  # 中等组件
            title_size = 14
            status_size = 11
            temp_size = 11
            temp_title_size = 9
            time_size = 9
        else:  # 小组件
            title_size = 12
            status_size = 10
            temp_size = 10
            temp_title_size = 8
            time_size = 8

        # 应用字体大小
        self.title_font.setPointSize(title_size)
        self.title_label.setFont(self.title_font)

        self.status_font.setPointSize(status_size)
        self.status_label.setFont(self.status_font)

        # 更新温度标签字体
        if hasattr(self, 'pv_font'):
            self.pv_font.setPointSize(temp_size)
            self.pv_label.setFont(self.pv_font)

        if hasattr(self, 'sv_font'):
            self.sv_font.setPointSize(temp_size)
            self.sv_label.setFont(self.sv_font)

        # 更新温度标题字体
        if hasattr(self, 'pv_title_font'):
            self.pv_title_font.setPointSize(temp_title_size)
            self.pv_title.setFont(self.pv_title_font)

        if hasattr(self, 'sv_title_font'):
            self.sv_title_font.setPointSize(temp_title_size)
            self.sv_title.setFont(self.sv_title_font)

        # 更新时间信息字体
        if hasattr(self, 'time_font'):
            self.time_font.setPointSize(time_size)
            self.time_info_label.setFont(self.time_font)

    def set_preferred_size(self, width, height):
        """设置首选尺寸"""
        self.setMinimumSize(width, height)
        self.setMaximumSize(width, height)
        self.resize(width, height)
        self.updateGeometry()

    def resizeEvent(self, event):
        """组件大小变化事件"""
        super().resizeEvent(event)
        # 延迟调整字体，确保尺寸变化完成
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(10, self.adjust_font_sizes)
    
    def update_data(self, furnace_data: FurnaceData):
        """更新炉子数据"""
        try:
            # 更新温度显示
            self.pv_label.setText(f"{furnace_data.current_temp:.1f}°C")
            self.sv_label.setText(f"{furnace_data.set_temp:.1f}°C")
            
            # 更新状态
            self.set_status_style(furnace_data.status)
            
            # 更新时间信息
            self.update_time_info(furnace_data)
            
            # 更新按钮状态
            self.update_button_states(furnace_data.status)
            
        except Exception as e:
            print(f"更新炉子{self.furnace_id}数据失败: {e}")
    
    def update_time_info(self, furnace_data: FurnaceData):
        """更新时间信息"""
        try:
            if furnace_data.status == FurnaceStatus.WORKING:
                if furnace_data.task_start_time:
                    start_str = furnace_data.task_start_time.strftime("%H:%M:%S")
                    if furnace_data.task_end_time:
                        end_str = furnace_data.task_end_time.strftime("%H:%M:%S")
                        self.time_info_label.setText(f"开始: {start_str}\n预计结束: {end_str}")
                    else:
                        self.time_info_label.setText(f"开始时间: {start_str}")
                else:
                    self.time_info_label.setText("工作中")
            elif furnace_data.status == FurnaceStatus.IDLE:
                if furnace_data.next_task_time:
                    next_str = furnace_data.next_task_time.strftime("%H:%M:%S")
                    self.time_info_label.setText(f"下次任务: {next_str}")
                else:
                    self.time_info_label.setText("空闲中")
            elif furnace_data.status == FurnaceStatus.FAULT:
                self.time_info_label.setText("设备故障")
            elif furnace_data.status == FurnaceStatus.COMM_ERROR:
                self.time_info_label.setText("通讯中断")
                
        except Exception as e:
            print(f"更新时间信息失败: {e}")
    
    def set_status_style(self, status: FurnaceStatus):
        """设置状态样式"""
        try:
            status_text = f"状态: {status.value}"
            self.status_label.setText(status_text)
            
            # 根据状态设置颜色
            if status == FurnaceStatus.WORKING:
                color = "green"
            elif status == FurnaceStatus.IDLE:
                color = "blue"
            elif status == FurnaceStatus.FAULT:
                color = "red"
            elif status == FurnaceStatus.COMM_ERROR:
                color = "gray"
            else:
                color = "black"
            
            # 如果有报警，使用红色背景
            if self.has_alarm:
                self.setStyleSheet(f"""
                    QWidget {{
                        background-color: #ffcccc;
                        border: 2px solid red;
                    }}
                    QLabel {{
                        color: {color};
                    }}
                """)
            else:
                self.setStyleSheet(f"""
                    QWidget {{
                        background-color: white;
                        border: 1px solid gray;
                    }}
                    QLabel {{
                        color: {color};
                    }}
                """)
                
        except Exception as e:
            print(f"设置状态样式失败: {e}")
    
    def set_alarm_state(self, has_alarm: bool):
        """设置报警状态"""
        self.has_alarm = has_alarm
        # 重新应用样式
        furnace_data = self.task_manager.comm_manager.get_furnace_data(self.furnace_id)
        if furnace_data:
            self.set_status_style(furnace_data.status)
    
    def update_button_states(self, status: FurnaceStatus):
        """更新按钮状态"""
        try:
            # 详情按钮始终可用
            self.detail_btn.setEnabled(True)
            
            # 停止按钮只在工作状态下可用
            is_working = (status == FurnaceStatus.WORKING and 
                         self.furnace_id in self.task_manager.get_running_tasks())
            self.stop_btn.setEnabled(is_working)
            
        except Exception as e:
            print(f"更新按钮状态失败: {e}")
    
    def update_display(self):
        """更新显示"""
        try:
            furnace_data = self.task_manager.comm_manager.get_furnace_data(self.furnace_id)
            if furnace_data:
                self.update_data(furnace_data)
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    @pyqtSlot()
    def show_detail(self):
        """显示详情"""
        try:
            # 延迟导入避免循环导入
            from .task_detail_window import TaskDetailWindow

            if not self.task_detail_window:
                self.task_detail_window = TaskDetailWindow(self.furnace_id, self.task_manager)

            self.task_detail_window.show()
            self.task_detail_window.raise_()

        except Exception as e:
            print(f"显示详情失败: {e}")
            QMessageBox.warning(self, "错误", f"显示详情失败: {e}")
    
    @pyqtSlot()
    def stop_task(self):
        """停止任务"""
        try:
            reply = QMessageBox.question(self, '确认停止', 
                                       f'确定要停止{self.furnace_id}号炉的任务吗？',
                                       QMessageBox.StandardButton.Yes | 
                                       QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                success = self.task_manager.stop_task(self.furnace_id)
                if success:
                    QMessageBox.information(self, "成功", "任务已停止")
                else:
                    QMessageBox.warning(self, "失败", "停止任务失败")
                    
        except Exception as e:
            print(f"停止任务失败: {e}")
            QMessageBox.warning(self, "错误", f"停止任务失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.show_detail()
        super().mousePressEvent(event)
