@echo off
echo 运行调试测试...
echo.

REM 尝试不同的Python命令
echo 尝试 python...
python debug_test.py
if %errorlevel% equ 0 goto success

echo 尝试 python3...
python3 debug_test.py
if %errorlevel% equ 0 goto success

echo 尝试 py...
py debug_test.py
if %errorlevel% equ 0 goto success

echo 尝试 py -3...
py -3 debug_test.py
if %errorlevel% equ 0 goto success

echo 尝试完整路径...
C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe debug_test.py
if %errorlevel% equ 0 goto success

echo 所有Python命令都失败了
echo 请检查Python安装
pause
exit /b 1

:success
echo 测试完成
pause
