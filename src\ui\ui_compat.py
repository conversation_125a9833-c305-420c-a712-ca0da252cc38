#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI兼容性导入文件
支持在新旧UI之间切换
"""

import os

# 环境变量控制UI版本
USE_DESIGNER_UI = os.getenv('USE_DESIGNER_UI', 'true').lower() == 'true'

if USE_DESIGNER_UI:
    try:
        from .main_window_new import MainWindow
        from .furnace_widget_new import FurnaceWidget
        print("✓ 使用Qt Designer重构版UI")
    except ImportError as e:
        print(f"⚠️  Designer UI导入失败，回退到原版: {e}")
        from .main_window import MainWindow
        from .furnace_widget import FurnaceWidget
else:
    from .main_window import MainWindow
    from .furnace_widget import FurnaceWidget
    print("✓ 使用原版UI")

__all__ = ['MainWindow', 'FurnaceWidget']
