#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器 - 系统核心业务逻辑
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from ..data.database_manager import DatabaseManager
from ..communication.communication_manager import CommunicationManager
from ..models.task import Task, TaskStatus, TemperatureRecord
from ..models.process import Process, ProcessStep
from ..models.furnace import FurnaceData, FurnaceStatus, AlarmData


class TaskManager(QObject):
    """任务管理器"""
    
    # 信号定义
    task_started = pyqtSignal(str)           # 任务开始
    task_completed = pyqtSignal(str)         # 任务完成
    task_stopped = pyqtSignal(str)           # 任务停止
    task_failed = pyqtSignal(str)            # 任务失败
    alarm_triggered = pyqtSignal(int, str)   # 报警触发
    alarm_cleared = pyqtSignal(int, str)     # 报警清除
    
    def __init__(self, db_manager: DatabaseManager, comm_manager: CommunicationManager):
        super().__init__()
        
        self.db_manager = db_manager
        self.comm_manager = comm_manager
        
        # 运行中的任务
        self.running_tasks: Dict[int, Task] = {}  # furnace_id -> Task
        
        # 任务执行状态
        self.task_execution_data: Dict[str, dict] = {}  # task_id -> execution_data
        
        # 报警状态
        self.active_alarms: Dict[int, List[AlarmData]] = {}  # furnace_id -> alarms
        
        # 定时器
        self.task_scheduler_timer = QTimer()
        self.task_scheduler_timer.timeout.connect(self._check_scheduled_tasks)
        self.task_scheduler_timer.start(10000)  # 每10秒检查一次
        
        self.data_logger_timer = QTimer()
        self.data_logger_timer.timeout.connect(self._log_temperature_data)
        self.data_logger_timer.start(10000)  # 每10秒记录一次数据
        
        self.alarm_checker_timer = QTimer()
        self.alarm_checker_timer.timeout.connect(self._check_alarms)
        self.alarm_checker_timer.start(5000)  # 每5秒检查一次报警
        
        # 连接通讯管理器信号
        self.comm_manager.furnace_data_updated.connect(self._on_furnace_data_updated)
        self.comm_manager.communication_error.connect(self._on_communication_error)
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化任务管理器"""
        # 恢复运行中的任务
        self._restore_running_tasks()
        
        # 初始化报警状态
        for furnace_id in range(1, 9):
            self.active_alarms[furnace_id] = []
    
    def _restore_running_tasks(self):
        """恢复运行中的任务"""
        try:
            # 查找状态为RUNNING的任务
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT task_id, furnace_id FROM tasks 
                    WHERE status = ?
                ''', (TaskStatus.RUNNING.value,))
                
                for row in cursor.fetchall():
                    task_id = row['task_id']
                    furnace_id = row['furnace_id']
                    
                    task = self.db_manager.get_task(task_id)
                    if task:
                        self.running_tasks[furnace_id] = task
                        
                        # 初始化执行数据
                        process = self.db_manager.get_process(task.process_id)
                        if process:
                            self.task_execution_data[task_id] = {
                                'process': process,
                                'start_time': task.actual_start_time or datetime.now(),
                                'current_step': task.current_step,
                                'step_start_time': datetime.now(),
                                'last_temp_check': datetime.now()
                            }
                        
                        print(f"恢复运行中任务: {task_id} (炉子{furnace_id})")
                        
        except Exception as e:
            print(f"恢复运行中任务失败: {e}")
    
    def create_task(self, process_id: str, furnace_id: int, 
                   scheduled_start_time: datetime) -> Optional[str]:
        """创建新任务"""
        try:
            # 检查工艺是否存在
            process = self.db_manager.get_process(process_id)
            if not process:
                print(f"工艺不存在: {process_id}")
                return None
            
            # 检查炉子是否可用
            if furnace_id in self.running_tasks:
                print(f"炉子{furnace_id}正在执行任务")
                return None
            
            # 创建任务
            task = Task(
                task_id=None,
                process_id=process_id,
                furnace_id=furnace_id,
                status=TaskStatus.PENDING,
                scheduled_start_time=scheduled_start_time
            )
            
            # 保存到数据库
            task_id = self.db_manager.save_task(task)
            task.task_id = task_id
            
            print(f"创建任务成功: {task_id}")
            return task_id
            
        except Exception as e:
            print(f"创建任务失败: {e}")
            return None
    
    def start_task(self, task_id: str) -> bool:
        """手动启动任务"""
        try:
            task = self.db_manager.get_task(task_id)
            if not task:
                print(f"任务不存在: {task_id}")
                return False
            
            if task.status != TaskStatus.PENDING:
                print(f"任务状态不正确: {task.status}")
                return False
            
            if task.furnace_id in self.running_tasks:
                print(f"炉子{task.furnace_id}正在执行其他任务")
                return False
            
            return self._execute_task(task)
            
        except Exception as e:
            print(f"启动任务失败: {e}")
            return False
    
    def stop_task(self, furnace_id: int) -> bool:
        """停止任务"""
        try:
            if furnace_id not in self.running_tasks:
                print(f"炉子{furnace_id}没有运行中的任务")
                return False
            
            task = self.running_tasks[furnace_id]
            
            # 更新任务状态
            task.stop_task()
            self.db_manager.update_task_status(task.task_id, TaskStatus.STOPPED)
            
            # 清理运行状态
            del self.running_tasks[furnace_id]
            if task.task_id in self.task_execution_data:
                del self.task_execution_data[task.task_id]
            
            # 发送信号
            self.task_stopped.emit(task.task_id)
            
            print(f"任务已停止: {task.task_id}")
            return True

        except Exception as e:
            print(f"停止任务失败: {e}")
            return False

    def stop_all_tasks(self) -> int:
        """停止所有正在运行的任务"""
        stopped_count = 0

        try:
            # 获取所有运行中的任务
            running_furnaces = list(self.running_tasks.keys())

            for furnace_id in running_furnaces:
                if self.stop_task(furnace_id):
                    stopped_count += 1

            print(f"已停止 {stopped_count} 个任务")
            return stopped_count

        except Exception as e:
            print(f"停止所有任务失败: {e}")
            return stopped_count

    def _execute_task(self, task: Task) -> bool:
        """执行任务"""
        try:
            # 获取工艺程序
            process = self.db_manager.get_process(task.process_id)
            if not process:
                print(f"工艺程序不存在: {task.process_id}")
                return False

            # 检查炉子状态
            furnace_data = self.comm_manager.get_furnace_data(task.furnace_id)
            if not furnace_data or furnace_data.status == FurnaceStatus.COMM_ERROR:
                print(f"炉子{task.furnace_id}通讯异常")
                return False

            # 启动任务
            task.start_task()
            self.db_manager.update_task_status(task.task_id, TaskStatus.RUNNING)

            # 添加到运行任务列表
            self.running_tasks[task.furnace_id] = task

            # 初始化执行数据
            self.task_execution_data[task.task_id] = {
                'process': process,
                'start_time': task.actual_start_time,
                'current_step': 0,
                'step_start_time': datetime.now(),
                'last_temp_check': datetime.now()
            }

            # 开始第一步
            if process.steps:
                first_step = process.steps[0]
                success = self.comm_manager.write_set_temperature(
                    task.furnace_id, first_step.target_temp
                )

                if not success:
                    print(f"设置初始温度失败: 炉子{task.furnace_id}")
                    self._fail_task(task.task_id)
                    return False

            # 发送信号
            self.task_started.emit(task.task_id)

            print(f"任务已启动: {task.task_id}")
            return True

        except Exception as e:
            print(f"执行任务失败: {e}")
            return False

    def _check_scheduled_tasks(self):
        """检查计划任务"""
        try:
            current_time = datetime.now()
            pending_tasks = self.db_manager.get_pending_tasks()

            for task in pending_tasks:
                # 检查是否到了执行时间
                if task.scheduled_start_time <= current_time:
                    # 检查炉子是否可用
                    if task.furnace_id not in self.running_tasks:
                        furnace_data = self.comm_manager.get_furnace_data(task.furnace_id)
                        if furnace_data and furnace_data.status != FurnaceStatus.COMM_ERROR:
                            print(f"自动启动计划任务: {task.task_id}")
                            self._execute_task(task)
                        else:
                            print(f"炉子{task.furnace_id}状态异常，延迟执行任务{task.task_id}")
                    else:
                        print(f"炉子{task.furnace_id}忙碌，延迟执行任务{task.task_id}")

        except Exception as e:
            print(f"检查计划任务失败: {e}")

    def _log_temperature_data(self):
        """记录温度数据"""
        try:
            for furnace_id, task in self.running_tasks.items():
                furnace_data = self.comm_manager.get_furnace_data(furnace_id)
                if furnace_data:
                    # 获取当前步骤
                    execution_data = self.task_execution_data.get(task.task_id)
                    current_step = execution_data['current_step'] if execution_data else 0

                    # 保存温度记录
                    self.db_manager.save_temperature_record(
                        task.task_id,
                        furnace_data.set_temp,
                        furnace_data.current_temp,
                        current_step
                    )

                    # 添加到任务对象
                    task.add_temperature_record(
                        furnace_data.set_temp,
                        furnace_data.current_temp,
                        current_step
                    )

        except Exception as e:
            print(f"记录温度数据失败: {e}")

    def _check_alarms(self):
        """检查报警条件"""
        try:
            for furnace_id in range(1, 9):
                furnace_data = self.comm_manager.get_furnace_data(furnace_id)
                if not furnace_data:
                    continue

                # 检查通讯中断
                if furnace_data.status == FurnaceStatus.COMM_ERROR:
                    self._trigger_alarm(furnace_id, "通讯中断", "设备通讯中断")
                else:
                    self._clear_alarm(furnace_id, "通讯中断")

                # 检查温度超差（仅对运行中的任务）
                if furnace_id in self.running_tasks:
                    temp_diff = abs(furnace_data.current_temp - furnace_data.set_temp)
                    if temp_diff > 5.0:  # 超差阈值5度
                        self._trigger_alarm(furnace_id, "温度超差",
                                          f"温度偏差{temp_diff:.1f}°C，超过允许范围")
                    else:
                        self._clear_alarm(furnace_id, "温度超差")

                # 检查设备故障
                if furnace_data.status == FurnaceStatus.FAULT:
                    self._trigger_alarm(furnace_id, "设备故障", "仪表报告设备故障")
                else:
                    self._clear_alarm(furnace_id, "设备故障")

        except Exception as e:
            print(f"检查报警失败: {e}")

    def _trigger_alarm(self, furnace_id: int, alarm_type: str, message: str):
        """触发报警"""
        try:
            # 检查是否已存在相同类型的活动报警
            existing_alarms = self.active_alarms.get(furnace_id, [])
            for alarm in existing_alarms:
                if alarm.alarm_type == alarm_type and alarm.is_active:
                    return  # 已存在相同报警

            # 创建新报警
            alarm = AlarmData(
                alarm_id=None,
                furnace_id=furnace_id,
                alarm_type=alarm_type,
                alarm_message=message,
                start_time=datetime.now(),
                is_active=True
            )

            # 保存到数据库
            alarm_id = self.db_manager.save_alarm(alarm)
            alarm.alarm_id = alarm_id

            # 添加到活动报警列表
            if furnace_id not in self.active_alarms:
                self.active_alarms[furnace_id] = []
            self.active_alarms[furnace_id].append(alarm)

            # 发送信号
            self.alarm_triggered.emit(furnace_id, f"{alarm_type}: {message}")

            print(f"报警触发: 炉子{furnace_id} - {alarm_type}: {message}")

        except Exception as e:
            print(f"触发报警失败: {e}")

    def _clear_alarm(self, furnace_id: int, alarm_type: str):
        """清除报警"""
        try:
            alarms = self.active_alarms.get(furnace_id, [])
            for alarm in alarms:
                if alarm.alarm_type == alarm_type and alarm.is_active:
                    # 更新报警状态
                    alarm.is_active = False
                    alarm.end_time = datetime.now()

                    # 更新数据库
                    self.db_manager.update_alarm(alarm.alarm_id, alarm.end_time, False)

                    # 发送信号
                    self.alarm_cleared.emit(furnace_id, f"{alarm_type}报警已清除")

                    print(f"报警清除: 炉子{furnace_id} - {alarm_type}")
                    break

        except Exception as e:
            print(f"清除报警失败: {e}")

    def _fail_task(self, task_id: str):
        """任务失败处理"""
        try:
            task = None
            furnace_id = None

            # 查找任务
            for fid, t in self.running_tasks.items():
                if t.task_id == task_id:
                    task = t
                    furnace_id = fid
                    break

            if not task:
                return

            # 更新任务状态
            task.fail_task()
            self.db_manager.update_task_status(task_id, TaskStatus.FAILED)

            # 清理运行状态
            if furnace_id:
                del self.running_tasks[furnace_id]
            if task_id in self.task_execution_data:
                del self.task_execution_data[task_id]

            # 发送信号
            self.task_failed.emit(task_id)

            print(f"任务失败: {task_id}")

        except Exception as e:
            print(f"任务失败处理异常: {e}")

    def _on_furnace_data_updated(self, furnace_id: int, furnace_data: FurnaceData):
        """炉子数据更新处理"""
        try:
            # 检查是否有运行中的任务需要处理工艺步骤
            if furnace_id in self.running_tasks:
                task = self.running_tasks[furnace_id]
                execution_data = self.task_execution_data.get(task.task_id)

                if execution_data:
                    self._process_task_step(task, execution_data, furnace_data)

        except Exception as e:
            print(f"处理炉子数据更新失败: {e}")

    def _on_communication_error(self, furnace_id: int, error_msg: str):
        """通讯错误处理"""
        self._trigger_alarm(furnace_id, "通讯错误", error_msg)

    def _process_task_step(self, task: Task, execution_data: dict, furnace_data: FurnaceData):
        """处理任务工艺步骤"""
        try:
            process = execution_data['process']
            current_step_index = execution_data['current_step']

            if current_step_index >= len(process.steps):
                # 所有步骤完成
                self._complete_task(task.task_id)
                return

            current_step = process.steps[current_step_index]
            step_start_time = execution_data['step_start_time']

            # 检查是否达到目标温度
            temp_diff = abs(furnace_data.current_temp - current_step.target_temp)

            if temp_diff <= 2.0:  # 温度误差在2度以内
                # 检查保温时间
                elapsed_time = (datetime.now() - step_start_time).total_seconds() / 60  # 分钟

                if elapsed_time >= current_step.duration:
                    # 当前步骤完成，进入下一步
                    next_step_index = current_step_index + 1

                    if next_step_index < len(process.steps):
                        next_step = process.steps[next_step_index]

                        # 设置下一步温度
                        success = self.comm_manager.write_set_temperature(
                            task.furnace_id, next_step.target_temp
                        )

                        if success:
                            # 更新执行数据
                            execution_data['current_step'] = next_step_index
                            execution_data['step_start_time'] = datetime.now()

                            # 更新数据库
                            self.db_manager.update_task_status(
                                task.task_id, TaskStatus.RUNNING, next_step_index
                            )

                            print(f"任务{task.task_id}进入步骤{next_step_index + 1}")
                        else:
                            print(f"设置下一步温度失败: 任务{task.task_id}")
                            self._fail_task(task.task_id)
                    else:
                        # 所有步骤完成
                        self._complete_task(task.task_id)

        except Exception as e:
            print(f"处理任务步骤失败: {e}")

    def _complete_task(self, task_id: str):
        """完成任务"""
        try:
            task = None
            furnace_id = None

            # 查找任务
            for fid, t in self.running_tasks.items():
                if t.task_id == task_id:
                    task = t
                    furnace_id = fid
                    break

            if not task:
                return

            # 更新任务状态
            task.complete_task()
            self.db_manager.update_task_status(task_id, TaskStatus.COMPLETED)

            # 导出CSV文件
            csv_filename = f"task_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            self.db_manager.export_task_data_to_csv(task_id, csv_filename)

            # 清理运行状态
            if furnace_id:
                del self.running_tasks[furnace_id]
            if task_id in self.task_execution_data:
                del self.task_execution_data[task_id]

            # 发送信号
            self.task_completed.emit(task_id)

            print(f"任务完成: {task_id}")

        except Exception as e:
            print(f"完成任务失败: {e}")

    def get_running_tasks(self) -> Dict[int, Task]:
        """获取运行中的任务"""
        return self.running_tasks.copy()

    def get_active_alarms(self, furnace_id: Optional[int] = None) -> List[AlarmData]:
        """获取活动报警"""
        if furnace_id:
            return [alarm for alarm in self.active_alarms.get(furnace_id, []) if alarm.is_active]
        else:
            all_alarms = []
            for alarms in self.active_alarms.values():
                all_alarms.extend([alarm for alarm in alarms if alarm.is_active])
            return all_alarms

    def cleanup(self):
        """清理资源"""
        try:
            # 停止定时器
            self.task_scheduler_timer.stop()
            self.data_logger_timer.stop()
            self.alarm_checker_timer.stop()

            print("任务管理器已清理")

        except Exception as e:
            print(f"清理任务管理器失败: {e}")
