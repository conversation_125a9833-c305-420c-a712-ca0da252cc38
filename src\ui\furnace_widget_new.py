#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的炉子监控组件 - 使用Qt Designer UI文件
实现界面设计与业务逻辑分离
"""

from PyQt6.QtWidgets import QFrame, QMessageBox, QSizePolicy
from PyQt6.QtCore import Qt, pyqtSlot, QTimer
from PyQt6.QtGui import QFont
from datetime import datetime

from ..models.furnace import Furnace<PERSON>tatus, FurnaceData
from ..core.task_manager import TaskManager

# 导入生成的UI类
from .ui_files.ui_furnace_widget import Ui_FurnaceWidget


class FurnaceWidget(QFrame):
    """炉子监控组件 - 重构版本"""
    
    def __init__(self, furnace_id: int, task_manager: TaskManager):
        super().__init__()
        
        # 业务逻辑相关
        self.furnace_id = furnace_id
        self.task_manager = task_manager
        self.task_detail_window = None
        self.has_alarm = False
        
        # 设置UI
        self.ui = Ui_FurnaceWidget()
        self.ui.setupUi(self)
        
        # 初始化界面
        self.init_ui()
        self.setup_connections()
        self.update_display()
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置尺寸策略
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        
        # 设置炉子编号
        self.ui.title_label.setText(f"{self.furnace_id}号炉")
        
        # 保存字体对象以便后续调整
        self.title_font = self.ui.title_label.font()
        self.status_font = self.ui.status_label.font()
        self.pv_font = self.ui.pv_label.font()
        self.sv_font = self.ui.sv_label.font()
        self.pv_title_font = self.ui.pv_title.font()
        self.sv_title_font = self.ui.sv_title.font()
        self.time_font = self.ui.time_info_label.font()
        
        # 设置默认样式
        self.set_status_style(FurnaceStatus.COMM_ERROR)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.ui.detail_btn.clicked.connect(self.show_detail)
        self.ui.stop_btn.clicked.connect(self.stop_task)
        
        # 鼠标点击事件
        self.mousePressEvent = self.on_mouse_press
    
    def set_preferred_size(self, width, height):
        """设置首选尺寸"""
        self.setMinimumSize(width, height)
        self.setMaximumSize(width, height)
        self.resize(width, height)
        self.updateGeometry()
    
    def adjust_font_sizes(self):
        """根据组件大小调整字体"""
        widget_width = self.width()
        widget_height = self.height()
        
        # 根据组件大小计算合适的字体大小
        if widget_width > 400:  # 大组件
            title_size = 16
            status_size = 12
            temp_size = 12
            temp_title_size = 10
            time_size = 10
        elif widget_width > 300:  # 中等组件
            title_size = 14
            status_size = 11
            temp_size = 11
            temp_title_size = 9
            time_size = 9
        else:  # 小组件
            title_size = 12
            status_size = 10
            temp_size = 10
            temp_title_size = 8
            time_size = 8
        
        # 应用字体大小
        self.title_font.setPointSize(title_size)
        self.ui.title_label.setFont(self.title_font)
        
        self.status_font.setPointSize(status_size)
        self.ui.status_label.setFont(self.status_font)
        
        # 更新温度标签字体
        self.pv_font.setPointSize(temp_size)
        self.ui.pv_label.setFont(self.pv_font)
        
        self.sv_font.setPointSize(temp_size)
        self.ui.sv_label.setFont(self.sv_font)
        
        # 更新温度标题字体
        self.pv_title_font.setPointSize(temp_title_size)
        self.ui.pv_title.setFont(self.pv_title_font)
        
        self.sv_title_font.setPointSize(temp_title_size)
        self.ui.sv_title.setFont(self.sv_title_font)
        
        # 更新时间信息字体
        self.time_font.setPointSize(time_size)
        self.ui.time_info_label.setFont(self.time_font)
    
    def resizeEvent(self, event):
        """组件大小变化事件"""
        super().resizeEvent(event)
        # 延迟调整字体，确保尺寸变化完成
        QTimer.singleShot(10, self.adjust_font_sizes)
    
    def update_data(self, furnace_data: FurnaceData):
        """更新炉子数据"""
        try:
            # 更新温度显示
            self.ui.pv_label.setText(f"{furnace_data.current_temp:.1f}°C")
            self.ui.sv_label.setText(f"{furnace_data.set_temp:.1f}°C")
            
            # 更新状态
            self.set_status_style(furnace_data.status)
            
            # 更新时间信息
            self.update_time_info(furnace_data)
            
            # 更新按钮状态
            self.update_button_states(furnace_data.status)
            
        except Exception as e:
            print(f"更新炉子{self.furnace_id}数据失败: {e}")
    
    def update_time_info(self, furnace_data: FurnaceData):
        """更新时间信息"""
        try:
            if furnace_data.status == FurnaceStatus.WORKING and furnace_data.task_id:
                # 显示任务信息
                task = self.task_manager.get_task(furnace_data.task_id)
                if task:
                    elapsed = datetime.now() - task.start_time
                    elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
                    
                    remaining_time = task.estimated_duration - elapsed.total_seconds()
                    if remaining_time > 0:
                        remaining_str = str(datetime.fromtimestamp(remaining_time) - datetime.fromtimestamp(0)).split('.')[0]
                        self.ui.time_info_label.setText(f"已运行: {elapsed_str}\n剩余: {remaining_str}")
                    else:
                        self.ui.time_info_label.setText(f"已运行: {elapsed_str}\n超时运行")
                else:
                    self.ui.time_info_label.setText("任务信息获取失败")
            else:
                self.ui.time_info_label.setText("空闲中")
                
        except Exception as e:
            print(f"更新时间信息失败: {e}")
            self.ui.time_info_label.setText("信息更新失败")
    
    def update_button_states(self, status: FurnaceStatus):
        """更新按钮状态"""
        try:
            # 停止按钮只在工作状态下可用
            self.ui.stop_btn.setEnabled(status == FurnaceStatus.WORKING)
            
        except Exception as e:
            print(f"更新按钮状态失败: {e}")
    
    def set_status_style(self, status: FurnaceStatus):
        """设置状态样式"""
        try:
            status_text = f"状态: {status.value}"
            self.ui.status_label.setText(status_text)
            
            # 根据状态设置颜色
            if status == FurnaceStatus.WORKING:
                color = "green"
            elif status == FurnaceStatus.IDLE:
                color = "blue"
            elif status == FurnaceStatus.FAULT:
                color = "red"
            elif status == FurnaceStatus.COMM_ERROR:
                color = "gray"
            else:
                color = "black"
            
            # 如果有报警，使用红色背景
            if self.has_alarm:
                self.setStyleSheet(f"""
                    QWidget {{
                        background-color: #ffcccc;
                        border: 2px solid red;
                    }}
                    QLabel {{
                        color: {color};
                    }}
                """)
            else:
                self.setStyleSheet(f"""
                    QWidget {{
                        background-color: white;
                        border: 1px solid gray;
                    }}
                    QLabel {{
                        color: {color};
                    }}
                """)
                
        except Exception as e:
            print(f"设置状态样式失败: {e}")
    
    def set_alarm(self, has_alarm: bool):
        """设置报警状态"""
        self.has_alarm = has_alarm
        # 重新应用样式
        furnace_data = self.task_manager.get_furnace_data(self.furnace_id)
        if furnace_data:
            self.set_status_style(furnace_data.status)
    
    def update_display(self):
        """更新显示"""
        try:
            furnace_data = self.task_manager.get_furnace_data(self.furnace_id)
            if furnace_data:
                self.update_data(furnace_data)
        except Exception as e:
            print(f"更新炉子{self.furnace_id}显示失败: {e}")
    
    @pyqtSlot()
    def show_detail(self):
        """显示详情窗口"""
        try:
            from .task_detail_window import TaskDetailWindow
            
            if self.task_detail_window is None:
                self.task_detail_window = TaskDetailWindow(self.furnace_id, self.task_manager)
            
            self.task_detail_window.show()
            self.task_detail_window.raise_()
            self.task_detail_window.activateWindow()
            
        except Exception as e:
            print(f"显示详情窗口失败: {e}")
            QMessageBox.warning(self, "错误", f"显示详情窗口失败: {e}")
    
    @pyqtSlot()
    def stop_task(self):
        """停止任务"""
        try:
            reply = QMessageBox.question(self, '确认停止', 
                                       f'确定要停止{self.furnace_id}号炉的任务吗？',
                                       QMessageBox.StandardButton.Yes | 
                                       QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                success = self.task_manager.stop_task(self.furnace_id)
                if success:
                    QMessageBox.information(self, "成功", "任务已停止")
                else:
                    QMessageBox.warning(self, "失败", "停止任务失败")
                    
        except Exception as e:
            print(f"停止任务失败: {e}")
            QMessageBox.warning(self, "错误", f"停止任务失败: {e}")
    
    def on_mouse_press(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.show_detail()
        super().mousePressEvent(event)
