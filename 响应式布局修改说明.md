# 8台炉子UI组件响应式布局修改说明

## 问题描述
原系统中8台炉子的UI组件存在以下问题：
1. 窗口缩小时组件会发生重叠
2. 缩小后变成两列四行，右边空白区域非常大
3. 排布不合理，空间利用率低
4. 每个炉子组件使用固定尺寸（280x200），缺乏自适应功能

## 解决方案

### 1. 炉子组件（FurnaceWidget）修改

**文件：** `src/ui/furnace_widget.py`

**主要修改：**
- 移除固定尺寸设置 `setFixedSize(280, 200)`
- 改为设置最小和最大尺寸：
  - 最小尺寸：200x150
  - 最大尺寸：400x300
- 优化内部布局间距和字体大小
- 添加文字换行支持

**具体变更：**
```python
# 原代码
self.setFixedSize(280, 200)

# 修改后
self.setMinimumSize(220, 160)
# 移除固定最大尺寸，改为在主窗口中动态设置
# 添加字体自适应功能

def set_preferred_size(self, width, height):
    """设置首选尺寸并强制应用"""
    self.setMinimumSize(width, height)
    self.setMaximumSize(width, height)
    self.resize(width, height)
    self.updateGeometry()

def adjust_font_sizes(self):
    """根据组件大小调整字体"""
    widget_width = self.width()
    if widget_width > 400:
        title_size, status_size = 16, 12
    elif widget_width > 300:
        title_size, status_size = 14, 11
    else:
        title_size, status_size = 12, 10
    # 应用字体大小...
```

### 2. 主窗口（MainWindow）修改

**文件：** `src/ui/main_window.py`

**主要修改：**
- 添加滚动区域支持，防止内容被截断
- 实现响应式网格布局
- 添加窗口大小变化事件处理
- 根据窗口宽度自动调整列数

**智能响应式布局规则：**
- 根据可用空间动态计算最佳列数
- 支持的布局选项：1列8行、2列4行、3列3行、4列2行
- 自动选择最适合当前窗口尺寸的布局
- 居中显示，消除大片空白区域

**核心功能：**
```python
def arrange_furnace_widgets(self):
    """智能布局安排"""
    # 计算可用空间
    available_width = self.width() - 40
    available_height = self.height() - 150

    # 智能选择最佳布局
    options = [(1,8), (2,4), (3,3), (4,2)]
    for cols, rows in options:
        if self.layout_fits(cols, rows, available_width, available_height):
            best_cols, best_rows = cols, rows

    # 动态调整组件尺寸
    self.adjust_widget_sizes(best_cols, available_width, available_height)

    # 重新排列并居中显示
    ...

def adjust_widget_sizes(self, cols, available_width, available_height):
    """动态调整组件尺寸以最佳利用空间"""
    ideal_width = (available_width - margins) // cols
    ideal_height = (available_height - margins) // rows

    # 根据窗口大小动态调整最大尺寸限制
    if window_width > 1600:  # 大窗口
        max_width = min(500, ideal_width)
        max_height = min(400, ideal_height)
    elif window_width > 1200:  # 中等窗口
        max_width = min(400, ideal_width)
        max_height = min(320, ideal_height)
    else:  # 小窗口
        max_width = min(350, ideal_width)
        max_height = min(280, ideal_height)
```

## 改进效果

### 1. 智能自适应布局
- ✅ 窗口缩小时组件不再重叠
- ✅ 智能选择最佳行列组合（1×8、2×4、3×3、4×2）
- ✅ 根据实际可用空间动态调整
- ✅ 支持滚动查看所有组件

### 2. 空间优化
- ✅ 消除大片空白区域
- ✅ 居中显示，左右对称
- ✅ 动态调整组件尺寸以最佳利用空间
- ✅ 合理的间距和边距设置

### 3. 更好的用户体验
- ✅ 小屏幕设备友好
- ✅ 组件尺寸智能缩放
- ✅ 文字内容完整显示
- ✅ 视觉效果更加平衡

### 4. 兼容性
- ✅ 保持原有功能不变
- ✅ 向后兼容现有配置
- ✅ 支持各种屏幕分辨率

## 测试建议

1. **窗口大小测试**
   - 将窗口调整到不同大小
   - 验证布局是否正确切换
   - 确认组件不会重叠

2. **屏幕分辨率测试**
   - 在不同分辨率显示器上测试
   - 验证小屏幕设备的显示效果
   - 确认大屏幕的布局合理性

3. **功能完整性测试**
   - 验证所有炉子组件功能正常
   - 确认点击、状态更新等操作无异常
   - 测试详情窗口和控制功能

## 技术细节

### 使用的Qt布局技术
- `QGridLayout`：网格布局管理器
- `QScrollArea`：滚动区域支持
- `QHBoxLayout`：水平布局实现居中
- `QSizePolicy`：尺寸策略控制
- `setMinimumSize/setMaximumSize`：动态尺寸约束
- `resizeEvent`：窗口大小变化事件

### 布局策略
- 智能计算最佳行列组合
- 使用容器布局实现居中显示
- 动态调整组件尺寸以最佳利用空间
- 使用拉伸因子确保均匀分布
- 清除未使用的拉伸因子避免布局问题

## 注意事项

1. 修改后的组件尺寸是动态的，在极小窗口下可能需要滚动查看
2. 字体大小已优化，但在极小尺寸下仍需注意可读性
3. 建议设置合理的窗口最小尺寸以保证基本可用性

## 本次修改重点解决的问题

### 空白区域问题
- **问题**：缩小窗口后右边出现大片空白区域
- **解决**：添加居中容器布局，使炉子组件区域居中显示
- **效果**：左右空白区域均匀分布，视觉效果更平衡

### 布局选择问题
- **问题**：简单的宽度判断导致布局不合理
- **解决**：智能计算可用空间，选择最适合的行列组合
- **效果**：在不同窗口尺寸下都能找到最佳布局

### 组件尺寸问题
- **问题**：固定尺寸无法适应不同布局需求，放大时不能同步放大
- **解决**：
  - 移除固定的最大尺寸限制
  - 动态计算理想尺寸并强制应用
  - 添加 `set_preferred_size()` 方法确保组件使用计算出的尺寸
  - 使用延迟更新机制确保尺寸变化生效
- **效果**：组件大小随布局和可用空间智能调整，真正支持同步缩放

### 字体缩放问题
- **问题**：字体大小固定，在大组件中显示过小
- **解决**：添加字体自适应功能，根据组件大小动态调整字体
- **效果**：字体大小与组件尺寸协调，提升可读性

### 尺寸策略问题
- **问题**：组件的尺寸策略不正确，无法响应布局变化
- **解决**：调整尺寸策略为 `Preferred`，并强制设置首选尺寸
- **效果**：组件能够正确响应布局管理器的尺寸分配

## 后续优化建议

1. 可以考虑添加字体大小自适应功能
2. 可以添加用户自定义布局选项
3. 可以考虑添加组件密度设置（紧凑/正常/宽松）
4. 可以添加布局预览功能，让用户选择偏好的布局方式
