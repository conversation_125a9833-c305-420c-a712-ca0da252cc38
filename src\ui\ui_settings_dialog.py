#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI设置对话框 - 允许用户自定义窗口尺寸和位置
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QSpinBox, QGroupBox, QGridLayout,
                             QComboBox, QCheckBox, QTabWidget, QWidget,
                             QMessageBox, QApplication)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
import json
import os


class UISettingsDialog(QDialog):
    """UI设置对话框"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings_file = "ui_settings.json"
        self.current_settings = self.load_settings()
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("界面设置")
        self.setFixedSize(500, 600)
        
        # 居中显示
        screen = QApplication.primaryScreen().availableGeometry()
        x = (screen.width() - 500) // 2
        y = (screen.height() - 600) // 2
        self.setGeometry(x, y, 500, 600)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("界面设置")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 窗口尺寸选项卡
        size_tab = self.create_size_tab()
        tab_widget.addTab(size_tab, "窗口尺寸")
        
        # 显示选项卡
        display_tab = self.create_display_tab()
        tab_widget.addTab(display_tab, "显示选项")
        
        layout.addWidget(tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        reset_btn = QPushButton("恢复默认")
        reset_btn.clicked.connect(self.reset_to_default)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(apply_btn)
        
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_settings)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
    
    def create_size_tab(self):
        """创建尺寸设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主窗口设置
        main_group = QGroupBox("主窗口")
        main_layout = QGridLayout(main_group)
        
        main_layout.addWidget(QLabel("宽度:"), 0, 0)
        self.main_width = QSpinBox()
        self.main_width.setRange(800, 2000)
        self.main_width.setSuffix(" px")
        main_layout.addWidget(self.main_width, 0, 1)
        
        main_layout.addWidget(QLabel("高度:"), 0, 2)
        self.main_height = QSpinBox()
        self.main_height.setRange(600, 1500)
        self.main_height.setSuffix(" px")
        main_layout.addWidget(self.main_height, 0, 3)
        
        layout.addWidget(main_group)
        
        # 弹窗设置
        dialog_group = QGroupBox("弹窗尺寸")
        dialog_layout = QGridLayout(dialog_group)
        
        # 工艺管理窗口
        dialog_layout.addWidget(QLabel("工艺管理:"), 0, 0)
        self.process_width = QSpinBox()
        self.process_width.setRange(600, 1600)
        self.process_width.setSuffix(" px")
        dialog_layout.addWidget(self.process_width, 0, 1)
        
        self.process_height = QSpinBox()
        self.process_height.setRange(500, 1200)
        self.process_height.setSuffix(" px")
        dialog_layout.addWidget(self.process_height, 0, 2)
        
        # 任务调度窗口
        dialog_layout.addWidget(QLabel("任务调度:"), 1, 0)
        self.scheduler_width = QSpinBox()
        self.scheduler_width.setRange(800, 1600)
        self.scheduler_width.setSuffix(" px")
        dialog_layout.addWidget(self.scheduler_width, 1, 1)
        
        self.scheduler_height = QSpinBox()
        self.scheduler_height.setRange(600, 1200)
        self.scheduler_height.setSuffix(" px")
        dialog_layout.addWidget(self.scheduler_height, 1, 2)
        
        # 任务详情窗口
        dialog_layout.addWidget(QLabel("任务详情:"), 2, 0)
        self.detail_width = QSpinBox()
        self.detail_width.setRange(600, 1400)
        self.detail_width.setSuffix(" px")
        dialog_layout.addWidget(self.detail_width, 2, 1)
        
        self.detail_height = QSpinBox()
        self.detail_height.setRange(500, 1000)
        self.detail_height.setSuffix(" px")
        dialog_layout.addWidget(self.detail_height, 2, 2)
        
        # 历史查询窗口
        dialog_layout.addWidget(QLabel("历史查询:"), 3, 0)
        self.history_width = QSpinBox()
        self.history_width.setRange(800, 1600)
        self.history_width.setSuffix(" px")
        dialog_layout.addWidget(self.history_width, 3, 1)
        
        self.history_height = QSpinBox()
        self.history_height.setRange(500, 1000)
        self.history_height.setSuffix(" px")
        dialog_layout.addWidget(self.history_height, 3, 2)
        
        # 报警窗口
        dialog_layout.addWidget(QLabel("报警窗口:"), 4, 0)
        self.alarm_width = QSpinBox()
        self.alarm_width.setRange(600, 1200)
        self.alarm_width.setSuffix(" px")
        dialog_layout.addWidget(self.alarm_width, 4, 1)
        
        self.alarm_height = QSpinBox()
        self.alarm_height.setRange(400, 800)
        self.alarm_height.setSuffix(" px")
        dialog_layout.addWidget(self.alarm_height, 4, 2)
        
        layout.addWidget(dialog_group)
        
        layout.addStretch()
        return widget
    
    def create_display_tab(self):
        """创建显示选项选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 窗口行为
        behavior_group = QGroupBox("窗口行为")
        behavior_layout = QVBoxLayout(behavior_group)
        
        self.center_windows = QCheckBox("新窗口自动居中")
        behavior_layout.addWidget(self.center_windows)
        
        self.remember_position = QCheckBox("记住窗口位置")
        behavior_layout.addWidget(self.remember_position)
        
        self.auto_resize = QCheckBox("根据屏幕尺寸自动调整")
        behavior_layout.addWidget(self.auto_resize)
        
        layout.addWidget(behavior_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout(font_group)
        
        font_layout.addWidget(QLabel("界面字体大小:"), 0, 0)
        self.font_size = QComboBox()
        self.font_size.addItems(["8", "9", "10", "11", "12", "14", "16"])
        font_layout.addWidget(self.font_size, 0, 1)
        
        layout.addWidget(font_group)
        
        layout.addStretch()
        return widget
    
    def load_settings(self):
        """加载设置"""
        default_settings = {
            "main_window": {"width": 1400, "height": 900},
            "process_manager": {"width": 1200, "height": 800},
            "task_scheduler": {"width": 1300, "height": 850},
            "task_detail": {"width": 1100, "height": 750},
            "history_viewer": {"width": 1350, "height": 800},
            "alarm_window": {"width": 1000, "height": 650},
            "center_windows": True,
            "remember_position": False,
            "auto_resize": True,
            "font_size": "10"
        }
        
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    # 合并默认设置和加载的设置
                    default_settings.update(settings)
            except Exception as e:
                print(f"加载设置失败: {e}")
        
        return default_settings
    
    def save_settings(self):
        """保存设置"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"保存设置失败: {e}")
    
    def load_current_settings(self):
        """加载当前设置到界面"""
        # 主窗口
        self.main_width.setValue(self.current_settings["main_window"]["width"])
        self.main_height.setValue(self.current_settings["main_window"]["height"])
        
        # 弹窗
        self.process_width.setValue(self.current_settings["process_manager"]["width"])
        self.process_height.setValue(self.current_settings["process_manager"]["height"])
        
        self.scheduler_width.setValue(self.current_settings["task_scheduler"]["width"])
        self.scheduler_height.setValue(self.current_settings["task_scheduler"]["height"])
        
        self.detail_width.setValue(self.current_settings["task_detail"]["width"])
        self.detail_height.setValue(self.current_settings["task_detail"]["height"])
        
        self.history_width.setValue(self.current_settings["history_viewer"]["width"])
        self.history_height.setValue(self.current_settings["history_viewer"]["height"])
        
        self.alarm_width.setValue(self.current_settings["alarm_window"]["width"])
        self.alarm_height.setValue(self.current_settings["alarm_window"]["height"])
        
        # 显示选项
        self.center_windows.setChecked(self.current_settings["center_windows"])
        self.remember_position.setChecked(self.current_settings["remember_position"])
        self.auto_resize.setChecked(self.current_settings["auto_resize"])
        self.font_size.setCurrentText(self.current_settings["font_size"])
    
    def apply_settings(self):
        """应用设置"""
        # 更新设置
        self.current_settings.update({
            "main_window": {"width": self.main_width.value(), "height": self.main_height.value()},
            "process_manager": {"width": self.process_width.value(), "height": self.process_height.value()},
            "task_scheduler": {"width": self.scheduler_width.value(), "height": self.scheduler_height.value()},
            "task_detail": {"width": self.detail_width.value(), "height": self.detail_height.value()},
            "history_viewer": {"width": self.history_width.value(), "height": self.history_height.value()},
            "alarm_window": {"width": self.alarm_width.value(), "height": self.alarm_height.value()},
            "center_windows": self.center_windows.isChecked(),
            "remember_position": self.remember_position.isChecked(),
            "auto_resize": self.auto_resize.isChecked(),
            "font_size": self.font_size.currentText()
        })
        
        # 保存设置
        self.save_settings()
        
        # 发送信号
        self.settings_changed.emit()
        
        QMessageBox.information(self, "提示", "设置已应用！\n重启程序后完全生效。")
    
    def accept_settings(self):
        """确定并关闭"""
        self.apply_settings()
        self.accept()
    
    def reset_to_default(self):
        """恢复默认设置"""
        reply = QMessageBox.question(self, "确认", "确定要恢复默认设置吗？")
        if reply == QMessageBox.StandardButton.Yes:
            # 删除设置文件
            if os.path.exists(self.settings_file):
                os.remove(self.settings_file)
            
            # 重新加载默认设置
            self.current_settings = self.load_settings()
            self.load_current_settings()
            
            QMessageBox.information(self, "提示", "已恢复默认设置！")
