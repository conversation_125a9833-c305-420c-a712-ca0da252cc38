#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具栏样式配置
"""

def get_menubar_style():
    """获取菜单栏样式"""
    return """
    QMenuBar {
        background-color: #f0f0f0;
        border: 1px solid #d0d0d0;
        border-radius: 0px;
        padding: 2px;
        font-size: 11px;
        font-weight: normal;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 8px 12px;
        margin: 1px;
        border-radius: 3px;
    }
    
    QMenuBar::item:selected {
        background-color: #e0e0e0;
        border: 1px solid #c0c0c0;
    }
    
    QMenuBar::item:pressed {
        background-color: #d0d0d0;
    }
    
    QMenu {
        background-color: white;
        border: 1px solid #c0c0c0;
        border-radius: 3px;
        padding: 2px;
        font-size: 10px;
    }
    
    QMenu::item {
        background-color: transparent;
        padding: 6px 20px 6px 30px;
        margin: 1px;
        border-radius: 2px;
    }
    
    QMenu::item:selected {
        background-color: #e3f2fd;
        color: #1976d2;
    }
    
    QMenu::item:disabled {
        color: #999999;
    }
    
    QMenu::separator {
        height: 1px;
        background-color: #e0e0e0;
        margin: 3px 10px;
    }
    
    QMenu::indicator {
        width: 16px;
        height: 16px;
        left: 6px;
    }
    """

def get_main_window_style():
    """获取主窗口样式"""
    return """
    QMainWindow {
        background-color: #fafafa;
    }
    
    QLabel {
        font-size: 10px;
        color: #333333;
    }
    
    QLabel[class="title"] {
        font-size: 16px;
        font-weight: bold;
        color: #1976d2;
        padding: 10px;
    }
    
    QFrame {
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        background-color: white;
        margin: 2px;
    }
    
    QStatusBar {
        background-color: #f5f5f5;
        border-top: 1px solid #e0e0e0;
        font-size: 9px;
        color: #666666;
    }
    """

def apply_styles(main_window):
    """应用样式到主窗口"""
    # 应用菜单栏样式
    main_window.setStyleSheet(get_menubar_style() + get_main_window_style())
    
    # 设置标题标签的类名
    for child in main_window.findChildren(object):
        if hasattr(child, 'text') and hasattr(child, 'setProperty'):
            if "烧结炉集中控制与数据管理系统" in str(child.text()):
                child.setProperty("class", "title")
