# 烧结炉集中控制与数据管理系统

基于Python和PyQt6开发的工业控制系统，用于集中监控、控制和管理8台采用宇电AIBUS通讯协议的烧结炉。

## 功能特性

### 主要功能
- **实时监控**: 8台烧结炉的集中监控界面，实时显示温度、状态等信息
- **工艺管理**: 多段式工艺程序的创建、编辑和管理
- **任务调度**: 自动化任务调度和执行
- **数据记录**: 完整的温度曲线和任务历史记录
- **报警系统**: 通讯中断、温度超差、设备故障等报警功能
- **历史查询**: 任务历史查询和数据导出

### 技术特点
- **三层架构**: 表现层、业务逻辑层、数据访问层分离
- **AIBUS协议**: 支持宇电仪表的AIBUS通讯协议
- **SQLite数据库**: 本地数据存储和管理
- **实时图表**: 基于pyqtgraph的高性能温度曲线显示
- **模块化设计**: 高内聚、低耦合的模块设计

## 系统架构

```
烧结炉控制系统
├── src/
│   ├── models/          # 数据模型
│   │   ├── furnace.py   # 炉子数据模型
│   │   ├── process.py   # 工艺程序模型
│   │   └── task.py      # 任务模型
│   ├── data/            # 数据访问层
│   │   └── database_manager.py  # 数据库管理器
│   ├── communication/   # 通讯层
│   │   ├── aibus_protocol.py    # AIBUS协议实现
│   │   └── communication_manager.py  # 通讯管理器
│   ├── core/            # 业务逻辑层
│   │   └── task_manager.py      # 任务管理器
│   └── ui/              # 用户界面层
│       ├── main_window.py       # 主窗口
│       ├── furnace_widget.py    # 炉子监控组件
│       ├── process_manager_window.py    # 工艺管理窗口
│       ├── task_scheduler_window.py     # 任务调度窗口
│       ├── task_detail_window.py        # 任务详情窗口
│       ├── history_viewer_window.py     # 历史查询窗口
│       └── alarm_window.py              # 报警窗口
├── main.py              # 主程序入口
├── requirements.txt     # 依赖包列表
└── README.md           # 项目说明
```

## 安装和运行

### 环境要求
- Python 3.8+
- Windows 10/11 (推荐)
- 串口设备 (用于与仪表通讯)

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository_url>
   cd kongzhi
   ```

2. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行系统**
   ```bash
   python main.py
   ```

### 依赖包说明
- `PyQt6`: GUI框架
- `pyserial`: 串口通讯
- `pandas`: 数据处理
- `pyqtgraph`: 高性能图表
- `reportlab`: PDF报告生成
- `matplotlib`: 图表绘制
- `numpy`: 数值计算

## 使用说明

### 首次使用
1. 启动系统后会自动创建数据库文件
2. 在"工艺库管理"中创建工艺程序
3. 在"任务调度"中创建和管理任务
4. 主界面实时显示各炉子状态

### 工艺管理
1. 点击"工艺库管理"按钮
2. 创建新工艺，设置工艺名称和描述
3. 添加工艺步骤，设置目标温度和升温速率
4. 保存工艺程序

### 任务调度
1. 点击"任务调度"按钮
2. 选择工艺程序和目标炉子
3. 设置开始时间
4. 创建任务，系统将自动执行

### 监控和控制
1. 主界面显示8个炉子的实时状态
2. 点击炉子组件查看详细信息
3. 可以手动停止正在运行的任务
4. 报警信息会在界面上显示

## 配置说明

### 通讯配置
在 `src/communication/communication_manager.py` 中可以配置：
- 串口号 (默认: COM1)
- 波特率 (默认: 9600)
- 轮询间隔 (默认: 2秒)
- 设备地址映射

### 报警配置
在 `src/core/task_manager.py` 中可以配置：
- 温度超差阈值 (默认: ±5°C)
- 通讯超时时间 (默认: 3次轮询)
- 报警检查间隔 (默认: 5秒)

## 开发和测试

### 运行测试
```bash
python simple_test.py    # 简单测试
python test_system.py    # 完整测试
```

### 开发环境
推荐使用以下开发工具：
- PyCharm 或 VS Code
- Git 版本控制
- Python 虚拟环境

## 故障排除

### 常见问题
1. **串口通讯失败**
   - 检查串口号是否正确
   - 确认设备连接正常
   - 检查波特率设置

2. **界面无法启动**
   - 确认PyQt6已正确安装
   - 检查Python版本兼容性

3. **数据库错误**
   - 检查文件权限
   - 删除数据库文件重新初始化

### 日志查看
系统运行时会在控制台输出日志信息，包括：
- 通讯状态
- 任务执行情况
- 错误信息

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本系统为工业控制软件，请在生产环境中谨慎使用，建议先在测试环境中充分验证。
