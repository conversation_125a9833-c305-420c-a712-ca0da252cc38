#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI文件编译工具
将.ui文件转换为Python代码
"""

import os
import sys
import subprocess
from pathlib import Path


def compile_ui_file(ui_file, output_file):
    """编译单个UI文件"""
    try:
        # 使用pyuic6将.ui文件转换为.py文件
        cmd = [sys.executable, "-m", "PyQt6.uic.pyuic", ui_file, "-o", output_file]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ 编译成功: {ui_file} -> {output_file}")
            return True
        else:
            print(f"✗ 编译失败: {ui_file}")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 编译异常: {ui_file} - {e}")
        return False


def compile_all_ui_files():
    """编译所有UI文件"""
    current_dir = Path(__file__).parent
    ui_files = list(current_dir.glob("*.ui"))
    
    if not ui_files:
        print("没有找到.ui文件")
        return
    
    print(f"找到 {len(ui_files)} 个UI文件")
    
    success_count = 0
    for ui_file in ui_files:
        # 生成输出文件名
        output_file = current_dir / f"ui_{ui_file.stem}.py"
        
        if compile_ui_file(str(ui_file), str(output_file)):
            success_count += 1
    
    print(f"\n编译完成: {success_count}/{len(ui_files)} 个文件成功")


def main():
    """主函数"""
    print("UI文件编译工具")
    print("=" * 40)
    
    # 检查pyuic6是否可用
    try:
        result = subprocess.run([sys.executable, "-m", "PyQt6.uic.pyuic", "--help"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("错误: pyuic6不可用，请确保PyQt6已正确安装")
            return 1
    except Exception as e:
        print(f"错误: 无法运行pyuic6 - {e}")
        return 1
    
    compile_all_ui_files()
    return 0


if __name__ == "__main__":
    sys.exit(main())
