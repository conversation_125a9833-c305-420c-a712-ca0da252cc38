#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的主窗口 - 使用Qt Designer UI文件
实现界面设计与业务逻辑分离
"""

from PyQt6.QtWidgets import QMainWindow, QApplication
from PyQt6.QtCore import Qt, QTimer, pyqtSlot
from PyQt6.QtGui import QFont, QAction
from datetime import datetime
from typing import Dict

from ..core.task_manager import TaskManager
from ..models.furnace import FurnaceStatus
from .furnace_widget_new import FurnaceWidget
from .ui_config import UIConfig, apply_window_geometry
from .toolbar_style import apply_styles

# 导入生成的UI类
from .ui_files.ui_main_window import Ui_MainWindow


class MainWindow(QMainWindow):
    """主窗口类 - 重构版本"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        # 业务逻辑相关
        self.task_manager = task_manager
        self.furnace_widgets: Dict[int, FurnaceWidget] = {}
        self.update_timer = QTimer()
        
        # 设置UI
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        # 初始化界面
        self.init_ui()
        self.setup_connections()
        self.setup_timer()
    
    def init_ui(self):
        """初始化用户界面"""
        # 应用窗口几何设置
        apply_window_geometry(self, UIConfig.get_main_window_geometry)
        
        # 创建菜单
        self.create_menu_actions()
        
        # 创建炉子组件
        self.create_furnace_widgets()
        
        # 应用样式
        apply_styles(self)
        
        # 设置状态栏
        self.ui.statusbar.showMessage("系统已启动")
    
    def create_menu_actions(self):
        """创建菜单动作"""
        # 文件菜单
        self.action_exit = QAction("退出", self)
        self.action_exit.setShortcut("Ctrl+Q")
        self.action_exit.triggered.connect(self.close)
        self.ui.menu_file.addAction(self.action_exit)
        
        # 工艺菜单
        self.action_process_manager = QAction("工艺库管理", self)
        self.action_process_manager.triggered.connect(self.show_process_manager)
        self.ui.menu_process.addAction(self.action_process_manager)
        
        # 任务菜单
        self.action_task_scheduler = QAction("任务调度", self)
        self.action_task_scheduler.triggered.connect(self.show_task_scheduler)
        self.ui.menu_task.addAction(self.action_task_scheduler)
        
        # 查询菜单
        self.action_history_viewer = QAction("历史查询", self)
        self.action_history_viewer.triggered.connect(self.show_history_viewer)
        self.ui.menu_query.addAction(self.action_history_viewer)
        
        # 工具菜单
        self.action_ui_settings = QAction("界面设置", self)
        self.action_ui_settings.triggered.connect(self.show_ui_settings)
        self.ui.menu_tools.addAction(self.action_ui_settings)
        
        # 帮助菜单
        self.action_about = QAction("关于", self)
        self.action_about.triggered.connect(self.show_about)
        self.ui.menu_help.addAction(self.action_about)
    
    def create_furnace_widgets(self):
        """创建炉子监控组件"""
        # 创建8个炉子监控组件
        for i in range(1, 9):
            furnace_widget = FurnaceWidget(i, self.task_manager)
            self.furnace_widgets[i] = furnace_widget
        
        # 初始布局
        self.arrange_furnace_widgets()
    
    def arrange_furnace_widgets(self):
        """根据窗口大小安排炉子组件布局"""
        # 清除现有布局
        for i in reversed(range(self.ui.furnace_layout.count())):
            item = self.ui.furnace_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
        
        # 清除之前的拉伸因子
        for i in range(10):
            self.ui.furnace_layout.setColumnStretch(i, 0)
            self.ui.furnace_layout.setRowStretch(i, 0)
        
        # 获取当前窗口尺寸
        window_width = self.width()
        window_height = self.height()
        
        # 计算可用区域
        available_width = window_width - 40
        available_height = window_height - 100
        
        # 炉子组件的最小尺寸
        min_widget_width = 220
        min_widget_height = 160
        
        # 根据可用空间智能计算最佳列数
        max_cols_by_width = max(1, available_width // (min_widget_width + 10))
        
        # 对于8个组件，找到最佳的行列组合
        best_cols = 4
        best_rows = 2
        
        # 尝试不同的列数，找到最合适的布局
        options = [(1, 8), (2, 4), (3, 3), (4, 2)]
        
        for cols, rows in options:
            if cols <= max_cols_by_width:
                required_width = cols * min_widget_width + (cols - 1) * 10
                required_height = rows * min_widget_height + (rows - 1) * 10
                
                if required_width <= available_width and required_height <= available_height:
                    best_cols = cols
                    best_rows = rows
        
        # 如果窗口太小，强制使用1列
        if available_width < min_widget_width + 20:
            best_cols = 1
            best_rows = 8
        
        # 根据布局调整组件尺寸
        self.adjust_widget_sizes(best_cols, available_width, available_height)
        
        # 重新排列组件
        for i in range(1, 9):
            row = (i - 1) // best_cols
            col = (i - 1) % best_cols
            
            furnace_widget = self.furnace_widgets[i]
            self.ui.furnace_layout.addWidget(furnace_widget, row, col)
        
        # 设置拉伸因子
        for col in range(best_cols):
            self.ui.furnace_layout.setColumnStretch(col, 1)
        
        actual_rows = (8 + best_cols - 1) // best_cols
        for row in range(actual_rows):
            self.ui.furnace_layout.setRowStretch(row, 1)
        
        # 强制更新布局
        self.ui.furnace_layout.update()
        self.ui.furnace_frame.updateGeometry()
    
    def adjust_widget_sizes(self, cols, available_width, available_height):
        """根据布局调整组件尺寸"""
        spacing = 10
        margins = 30
        
        ideal_width = (available_width - margins - (cols - 1) * spacing) // cols
        rows = (8 + cols - 1) // cols
        ideal_height = (available_height - margins - (rows - 1) * spacing) // rows
        
        # 设置合理的尺寸范围
        min_width = 220
        min_height = 160
        
        # 根据窗口大小动态调整最大尺寸限制
        window_width = self.width()
        
        if window_width > 1600:
            max_width = 500
            max_height = 400
        elif window_width > 1200:
            max_width = 400
            max_height = 320
        else:
            max_width = 350
            max_height = 280
        
        # 使用理想尺寸，但限制在合理范围内
        final_width = max(min_width, min(max_width, ideal_width))
        final_height = max(min_height, min(max_height, ideal_height))
        
        # 应用到所有炉子组件
        for i in range(1, 9):
            widget = self.furnace_widgets[i]
            
            if cols == 1:
                single_col_width = min(600, available_width - 40)
                widget.set_preferred_size(single_col_width, final_height)
            else:
                widget.set_preferred_size(final_width, final_height)
    
    def setup_connections(self):
        """设置信号连接"""
        # 任务管理器信号连接
        if hasattr(self.task_manager, 'furnace_data_updated'):
            self.task_manager.furnace_data_updated.connect(self.update_furnace_data)
        if hasattr(self.task_manager, 'alarm_triggered'):
            self.task_manager.alarm_triggered.connect(self.handle_alarm)
    
    def setup_timer(self):
        """设置定时器"""
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        if hasattr(self, 'furnace_widgets'):
            # 延迟执行布局调整
            QTimer.singleShot(50, self.arrange_furnace_widgets)
    
    @pyqtSlot()
    def update_display(self):
        """更新显示"""
        try:
            # 更新状态栏时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.ui.statusbar.showMessage(f"系统运行中 - {current_time}")
            
            # 更新炉子数据
            for furnace_id in range(1, 9):
                furnace_data = self.task_manager.get_furnace_data(furnace_id)
                if furnace_data:
                    self.furnace_widgets[furnace_id].update_data(furnace_data)
                    
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    @pyqtSlot(int, object)
    def update_furnace_data(self, furnace_id: int, furnace_data):
        """更新炉子数据"""
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].update_data(furnace_data)
    
    @pyqtSlot(int, str, str)
    def handle_alarm(self, furnace_id: int, alarm_type: str, message: str):
        """处理报警"""
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].set_alarm(True)
        print(f"报警触发: 炉子{furnace_id} - {alarm_type}: {message}")
    
    # 菜单动作处理方法
    @pyqtSlot()
    def show_process_manager(self):
        """显示工艺管理窗口"""
        try:
            from .process_manager_window import ProcessManagerWindow
            self.process_manager_window = ProcessManagerWindow(self.task_manager)
            self.process_manager_window.show()
        except Exception as e:
            print(f"打开工艺管理窗口失败: {e}")
    
    @pyqtSlot()
    def show_task_scheduler(self):
        """显示任务调度窗口"""
        try:
            from .task_scheduler_window import TaskSchedulerWindow
            self.task_scheduler_window = TaskSchedulerWindow(self.task_manager)
            self.task_scheduler_window.show()
        except Exception as e:
            print(f"打开任务调度窗口失败: {e}")
    
    @pyqtSlot()
    def show_history_viewer(self):
        """显示历史查询窗口"""
        try:
            from .history_viewer_window import HistoryViewerWindow
            self.history_viewer_window = HistoryViewerWindow(self.task_manager)
            self.history_viewer_window.show()
        except Exception as e:
            print(f"打开历史查询窗口失败: {e}")
    
    @pyqtSlot()
    def show_ui_settings(self):
        """显示界面设置对话框"""
        try:
            from .ui_settings_dialog import UISettingsDialog
            dialog = UISettingsDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"打开界面设置失败: {e}")
    
    @pyqtSlot()
    def show_about(self):
        """显示关于对话框"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.about(self, "关于", 
                         "烧结炉集中控制与数据管理系统\n"
                         "版本: 1.0.0\n"
                         "基于PyQt6开发")
